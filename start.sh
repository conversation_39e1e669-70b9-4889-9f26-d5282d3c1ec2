#!/bin/bash

# LinkedIn Auto Job Applier - Unified Starter Script
# Tek komutla hem bot'u hem dashboard'u başlatır

# Renkli çıktı için
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Banner göster
show_banner() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                LinkedIn Auto Job Applier                     ║"
    echo "║                    Unified Starter                           ║"
    echo "╠══════════════════════════════════════════════════════════════╣"
    echo "║  🤖 Bot: Automated job applications                         ║"
    echo "║  📊 Dashboard: Web interface (http://localhost:5001)        ║"
    echo "║  ❓ Questions: Question management                           ║"
    echo "║  🎯 Non-Easy Apply: Manual application management           ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# Cleanup function
cleanup() {
    echo -e "\n${YELLOW}🛑 Shutdown signal received...${NC}"

    if [ ! -z "$DASHBOARD_PID" ]; then
        echo -e "${YELLOW}⏹️  Stopping Dashboard (PID: $DASHBOARD_PID)...${NC}"
        kill $DASHBOARD_PID 2>/dev/null
        wait $DASHBOARD_PID 2>/dev/null
        echo -e "${GREEN}✅ Dashboard stopped${NC}"
    fi

    if [ ! -z "$BOT_PID" ]; then
        echo -e "${YELLOW}⏹️  Stopping Bot (PID: $BOT_PID)...${NC}"
        kill $BOT_PID 2>/dev/null
        wait $BOT_PID 2>/dev/null
        echo -e "${GREEN}✅ Bot stopped${NC}"
    fi

    echo -e "${GREEN}✅ All processes cleaned up${NC}"
    exit 0
}

# Signal handler ayarla
trap cleanup SIGINT SIGTERM

# Gerekli dosyaları kontrol et
check_requirements() {
    echo -e "${CYAN}🔍 Gerekli dosyalar kontrol ediliyor...${NC}"
    
    if [ ! -f "app.py" ]; then
        echo -e "${RED}❌ app.py bulunamadı${NC}"
        return 1
    fi
    
    if [ ! -f "runAiBot.py" ]; then
        echo -e "${RED}❌ runAiBot.py bulunamadı${NC}"
        return 1
    fi
    
    if [ ! -d "venv" ]; then
        echo -e "${RED}❌ Virtual environment (venv) bulunamadı${NC}"
        return 1
    fi
    
    echo -e "${GREEN}✅ Tüm gerekli dosyalar mevcut${NC}"
    return 0
}

# Virtual environment'ı aktive et
activate_venv() {
    if [ -f "venv/bin/activate" ]; then
        source venv/bin/activate
        echo -e "${GREEN}✅ Virtual environment aktive edildi${NC}"
    elif [ -f "venv/Scripts/activate" ]; then
        source venv/Scripts/activate
        echo -e "${GREEN}✅ Virtual environment aktive edildi (Windows)${NC}"
    else
        echo -e "${YELLOW}⚠️  Virtual environment bulunamadı, system Python kullanılıyor${NC}"
    fi
}

# Dashboard başlat
start_dashboard() {
    echo -e "${CYAN}🚀 Dashboard başlatılıyor...${NC}"
    
    python app.py > dashboard.log 2>&1 &
    DASHBOARD_PID=$!
    
    # Dashboard'un başladığını kontrol et
    sleep 3
    if kill -0 $DASHBOARD_PID 2>/dev/null; then
        echo -e "${GREEN}✅ Dashboard başlatıldı (PID: $DASHBOARD_PID)${NC}"
        echo -e "${BLUE}📊 Web arayüzü: http://localhost:5001${NC}"
        return 0
    else
        echo -e "${RED}❌ Dashboard başlatılamadı${NC}"
        echo -e "${YELLOW}Log dosyasını kontrol edin: dashboard.log${NC}"
        return 1
    fi
}

# Bot başlat
start_bot() {
    echo -e "${CYAN}🤖 Bot başlatılıyor...${NC}"
    
    python runAiBot.py > bot.log 2>&1 &
    BOT_PID=$!
    
    # Bot'un başladığını kontrol et
    sleep 3
    if kill -0 $BOT_PID 2>/dev/null; then
        echo -e "${GREEN}✅ Bot başlatıldı (PID: $BOT_PID)${NC}"
        return 0
    else
        echo -e "${YELLOW}⚠️  Bot başlatılamadı (Dashboard yine de çalışıyor)${NC}"
        echo -e "${YELLOW}Log dosyasını kontrol edin: bot.log${NC}"
        BOT_PID=""
        return 1
    fi
}

# Durum göster
show_status() {
    echo -e "\n${PURPLE}📊 DURUM RAPORU${NC}"
    echo "=============================================="
    
    if [ ! -z "$DASHBOARD_PID" ] && kill -0 $DASHBOARD_PID 2>/dev/null; then
        echo -e "Dashboard    | PID: $DASHBOARD_PID | ${GREEN}🟢 Çalışıyor${NC}"
    else
        echo -e "Dashboard    | PID: -      | ${RED}🔴 Durdu${NC}"
    fi
    
    if [ ! -z "$BOT_PID" ] && kill -0 $BOT_PID 2>/dev/null; then
        echo -e "Bot          | PID: $BOT_PID | ${GREEN}🟢 Çalışıyor${NC}"
    else
        echo -e "Bot          | PID: -      | ${RED}🔴 Durdu${NC}"
    fi
    
    echo "=============================================="
    echo -e "${BLUE}📊 Dashboard: http://localhost:5001${NC}"
    echo -e "${BLUE}❓ Sorular: http://localhost:5001/questions${NC}"
    echo -e "${BLUE}🎯 Easy Apply Olmayan: http://localhost:5001/non-easy-apply${NC}"
    echo -e "${BLUE}🧪 Test: http://localhost:5001/test-i18n${NC}"
    echo "=============================================="
}

# Log takip et
tail_logs() {
    echo -e "${CYAN}📋 Log dosyaları takip ediliyor...${NC}"
    echo -e "${YELLOW}Ctrl+C ile ana menüye dönebilirsiniz${NC}"
    
    if [ -f "dashboard.log" ] && [ -f "bot.log" ]; then
        tail -f dashboard.log bot.log
    elif [ -f "dashboard.log" ]; then
        tail -f dashboard.log
    elif [ -f "bot.log" ]; then
        tail -f bot.log
    else
        echo -e "${RED}❌ Log dosyası bulunamadı${NC}"
    fi
}

# Etkileşimli menü
interactive_menu() {
    while true; do
        echo -e "\n${CYAN}KOMUTLAR:${NC}"
        echo "s - Durum göster"
        echo "l - Log dosyalarını takip et"
        echo "r - Process'leri yeniden başlat"
        echo "q - Çıkış"
        echo "----------------------------------------"
        
        read -p "Komut seçin (s/l/r/q): " choice
        
        case $choice in
            s|S)
                show_status
                ;;
            l|L)
                tail_logs
                ;;
            r|R)
                echo -e "${YELLOW}🔄 Process'ler yeniden başlatılıyor...${NC}"
                cleanup
                sleep 2
                main
                ;;
            q|Q)
                cleanup
                ;;
            *)
                echo -e "${RED}❌ Geçersiz komut!${NC}"
                ;;
        esac
    done
}

# Ana fonksiyon
main() {
    show_banner
    
    # Gerekli dosyaları kontrol et
    if ! check_requirements; then
        echo -e "${RED}❌ Gerekli dosyalar eksik. Lütfen kurulumu kontrol edin.${NC}"
        exit 1
    fi
    
    # Virtual environment'ı aktive et
    activate_venv
    
    echo -e "\n${CYAN}🚀 Uygulamalar başlatılıyor...${NC}\n"
    
    # Dashboard'u başlat
    if ! start_dashboard; then
        echo -e "${RED}❌ Dashboard başlatılamadı. Çıkılıyor.${NC}"
        exit 1
    fi
    
    # Biraz bekle
    sleep 2
    
    # Bot'u başlat
    start_bot
    
    echo -e "\n${GREEN}🎉 SİSTEM HAZIR!${NC}"
    show_status
    
    echo -e "\n${YELLOW}⌨️  Etkileşimli menü için komut girin veya Ctrl+C ile çıkış yapın${NC}"
    
    # Etkileşimli menüyü başlat
    interactive_menu
}

# Script'i çalıştır
main
