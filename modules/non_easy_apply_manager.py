"""
LinkedIn Auto Job Applier - Easy Apply <PERSON><PERSON>yan İş Yönetim Modülü
Bu modül Easy Apply özelliği olmayan ancak kriterlere uygun işleri yönetir.
"""

import json
import os
import csv
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from pathlib import Path
import hashlib

from modules.helpers import print_lg, critical_error_log

class NonEasyApplyManager:
    """Easy Apply olmayan işleri yöneten sınıf"""
    
    def __init__(self, data_file: str = "data/non_easy_apply_jobs.json"):
        self.data_file = Path(data_file)
        self.csv_file = Path("all excels/non_easy_apply_jobs.csv")
        
        # Dizinleri oluştur
        self.data_file.parent.mkdir(parents=True, exist_ok=True)
        self.csv_file.parent.mkdir(parents=True, exist_ok=True)
        
        self.data = self._load_data()
    
    def _load_data(self) -> Dict:
        """Veri dosyasını yükler"""
        try:
            if self.data_file.exists():
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                print_lg(f"Easy Apply olmayan işler yüklendi: {len(data.get('jobs', []))} iş")
                return data
            else:
                return self._create_default_structure()
        except Exception as e:
            print_lg(f"Veri dosyası yüklenirken hata: {e}")
            critical_error_log("Non Easy Apply Manager - Data Loading", e)
            return self._create_default_structure()
    
    def _create_default_structure(self) -> Dict:
        """Varsayılan veri yapısını oluşturur"""
        default_data = {
            "version": "1.0",
            "last_updated": datetime.now().isoformat(),
            "jobs": [],
            "statistics": {
                "total_jobs": 0,
                "applied_jobs": 0,
                "pending_jobs": 0,
                "ignored_jobs": 0
            },
            "settings": {
                "auto_score": True,
                "min_score_threshold": 0.6,
                "max_jobs_per_company": 5
            }
        }
        self._save_data(default_data)
        return default_data
    
    def _save_data(self, data: Optional[Dict] = None) -> bool:
        """Veriyi dosyaya kaydeder"""
        try:
            if data is None:
                data = self.data
            
            data["last_updated"] = datetime.now().isoformat()
            
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            # CSV dosyasını da güncelle
            self._export_to_csv()
            
            print_lg("Easy Apply olmayan işler kaydedildi")
            return True
            
        except Exception as e:
            print_lg(f"Veri kaydedilirken hata: {e}")
            critical_error_log("Non Easy Apply Manager - Data Saving", e)
            return False
    
    def _generate_job_id(self, job_link: str) -> str:
        """İş için benzersiz ID oluşturur"""
        return hashlib.md5(job_link.encode()).hexdigest()[:12]
    
    def _calculate_suitability_score(self, job_data: Dict) -> float:
        """İş uygunluk skorunu hesaplar (config/search.py ayarlarını kullanarak)"""
        try:
            # Import search config
            try:
                from config.search import (
                    search_terms, bad_words, current_experience,
                    job_type, on_site, did_masters, security_clearance
                )
            except ImportError:
                print_lg("Search config yüklenemedi, varsayılan değerler kullanılıyor")
                search_terms = ["Software Engineer", "Developer"]
                bad_words = []
                current_experience = 3
                job_type = ["Full-time"]
                on_site = ["Remote"]
                did_masters = True
                security_clearance = False

            score = 0.5  # Base score

            title = job_data.get('title', '').lower()
            description = job_data.get('description', '').lower()
            work_location = job_data.get('work_location', '').lower()
            work_style = job_data.get('work_style', '').lower()

            # 1. Search terms matching (high weight)
            search_match_score = 0
            for term in search_terms:
                if term.lower() in title:
                    search_match_score += 0.3  # Title match is very important
                elif term.lower() in description:
                    search_match_score += 0.1  # Description match is good
            score += min(search_match_score, 0.4)  # Cap at 0.4

            # 2. Bad words penalty (high weight)
            bad_word_penalty = 0
            for bad_word in bad_words:
                if bad_word.lower() in title:
                    bad_word_penalty += 0.3  # Heavy penalty for title
                elif bad_word.lower() in description:
                    bad_word_penalty += 0.1  # Moderate penalty for description
            score -= min(bad_word_penalty, 0.5)  # Cap penalty at 0.5

            # 3. Experience level matching
            experience_required = job_data.get('experience_required', 0)
            if isinstance(experience_required, int) and current_experience >= 0:
                if experience_required <= current_experience:
                    score += 0.2  # Perfect match
                elif experience_required <= current_experience + 2:
                    score += 0.1  # Close match
                elif experience_required > current_experience + 3:
                    score -= 0.2  # Too senior

            # 4. Work style preference
            work_style_score = 0
            for preferred_style in on_site:
                if preferred_style.lower() in work_style or preferred_style.lower() in work_location:
                    work_style_score += 0.15
            score += min(work_style_score, 0.15)

            # 5. Job type preference
            job_type_score = 0
            for preferred_type in job_type:
                if preferred_type.lower() in description:
                    job_type_score += 0.1
            score += min(job_type_score, 0.1)

            # 6. Masters degree bonus
            if did_masters and ('master' in description or 'msc' in description or 'ms ' in description):
                score += 0.1

            # 7. Security clearance penalty if not available
            if not security_clearance and ('clearance' in description or 'security' in description):
                score -= 0.2

            # 8. Company reputation (basic heuristic)
            company = job_data.get('company', '').lower()
            big_tech_companies = [
                'google', 'microsoft', 'amazon', 'apple', 'meta', 'facebook',
                'netflix', 'tesla', 'uber', 'airbnb', 'spotify', 'twitter'
            ]
            if any(big_company in company for big_company in big_tech_companies):
                score += 0.1

            # 9. Job posting freshness
            try:
                from datetime import datetime, timedelta
                date_posted = job_data.get('date_posted', '')
                if date_posted:
                    # Assume date_posted is in a parseable format
                    # Fresh jobs (within 3 days) get bonus
                    posted_date = datetime.strptime(date_posted, '%Y-%m-%d')
                    days_old = (datetime.now() - posted_date).days
                    if days_old <= 3:
                        score += 0.05
                    elif days_old > 14:
                        score -= 0.05
            except:
                pass  # Skip if date parsing fails

            # 10. Skills matching
            skills = job_data.get('skills', '').lower()
            preferred_skills = [
                'python', 'javascript', 'react', 'node.js', 'django', 'flask',
                'sql', 'git', 'docker', 'aws', 'kubernetes', 'mongodb'
            ]
            skills_match = sum(1 for skill in preferred_skills if skill in skills or skill in description)
            score += min(skills_match * 0.02, 0.1)  # Max 0.1 bonus for skills

            # Ensure score is between 0 and 1
            final_score = max(0.0, min(1.0, score))

            return final_score

        except Exception as e:
            print_lg(f"Skor hesaplanırken hata: {e}")
            return 0.5
    
    def add_job(self, 
                job_id: str,
                title: str,
                company: str,
                work_location: str,
                work_style: str,
                description: str,
                experience_required: int,
                skills: str,
                hr_name: str,
                hr_link: str,
                date_posted: str,
                job_link: str,
                external_link: str) -> bool:
        """Yeni iş ekler"""
        try:
            # Duplicate check
            for existing_job in self.data["jobs"]:
                if existing_job["job_id"] == job_id:
                    print_lg(f"İş zaten mevcut: {title} | {company}")
                    return False
            
            job_data = {
                "job_id": job_id,
                "title": title,
                "company": company,
                "work_location": work_location,
                "work_style": work_style,
                "description": description,
                "experience_required": experience_required,
                "skills": skills,
                "hr_name": hr_name,
                "hr_link": hr_link,
                "date_posted": date_posted,
                "date_found": datetime.now().isoformat(),
                "job_link": job_link,
                "external_link": external_link,
                "status": "pending",
                "suitability_score": 0.0,
                "priority": "medium",
                "notes": "",
                "applied_date": None,
                "application_status": "not_applied"
            }
            
            # Calculate suitability score
            if self.data["settings"]["auto_score"]:
                job_data["suitability_score"] = self._calculate_suitability_score(job_data)
                
                # Set priority based on score
                if job_data["suitability_score"] >= 0.8:
                    job_data["priority"] = "high"
                elif job_data["suitability_score"] >= 0.6:
                    job_data["priority"] = "medium"
                else:
                    job_data["priority"] = "low"
            
            self.data["jobs"].append(job_data)
            self.data["statistics"]["total_jobs"] += 1
            self.data["statistics"]["pending_jobs"] += 1
            
            self._save_data()
            
            print_lg(f"Easy Apply olmayan iş eklendi: {title} | {company} (Skor: {job_data['suitability_score']:.2f})")
            return True
            
        except Exception as e:
            print_lg(f"İş eklenirken hata: {e}")
            critical_error_log("Non Easy Apply Manager - Add Job", e)
            return False
    
    def get_jobs_by_priority(self, priority: str = None, limit: int = None) -> List[Dict]:
        """Önceliğe göre işleri getirir"""
        try:
            jobs = self.data["jobs"].copy()
            
            # Filter by priority if specified
            if priority:
                jobs = [job for job in jobs if job.get("priority") == priority]
            
            # Sort by suitability score (descending) and date found (descending)
            jobs.sort(key=lambda x: (x.get("suitability_score", 0), x.get("date_found", "")), reverse=True)
            
            # Apply limit if specified
            if limit:
                jobs = jobs[:limit]
            
            return jobs
            
        except Exception as e:
            print_lg(f"İşler getirilirken hata: {e}")
            return []
    
    def get_pending_jobs(self, min_score: float = None) -> List[Dict]:
        """Bekleyen işleri getirir"""
        try:
            min_threshold = min_score or self.data["settings"]["min_score_threshold"]
            
            pending_jobs = [
                job for job in self.data["jobs"] 
                if job.get("status") == "pending" 
                and job.get("suitability_score", 0) >= min_threshold
            ]
            
            # Sort by score and priority
            pending_jobs.sort(key=lambda x: (
                1 if x.get("priority") == "high" else 2 if x.get("priority") == "medium" else 3,
                -x.get("suitability_score", 0)
            ))
            
            return pending_jobs
            
        except Exception as e:
            print_lg(f"Bekleyen işler getirilirken hata: {e}")
            return []
    
    def update_job_status(self, job_id: str, status: str, notes: str = "") -> bool:
        """İş durumunu günceller"""
        try:
            for job in self.data["jobs"]:
                if job["job_id"] == job_id:
                    old_status = job.get("status")
                    job["status"] = status
                    job["notes"] = notes
                    
                    if status == "applied":
                        job["applied_date"] = datetime.now().isoformat()
                        job["application_status"] = "applied"
                        
                        # Update statistics
                        if old_status == "pending":
                            self.data["statistics"]["pending_jobs"] -= 1
                        self.data["statistics"]["applied_jobs"] += 1
                    
                    elif status == "ignored":
                        if old_status == "pending":
                            self.data["statistics"]["pending_jobs"] -= 1
                        self.data["statistics"]["ignored_jobs"] += 1
                    
                    self._save_data()
                    print_lg(f"İş durumu güncellendi: {job['title']} -> {status}")
                    return True
            
            print_lg(f"İş bulunamadı: {job_id}")
            return False
            
        except Exception as e:
            print_lg(f"İş durumu güncellenirken hata: {e}")
            return False
    
    def _export_to_csv(self) -> bool:
        """İşleri CSV dosyasına aktarır"""
        try:
            fieldnames = [
                'Job ID', 'Title', 'Company', 'Work Location', 'Work Style',
                'Description', 'Experience Required', 'Skills', 'HR Name', 'HR Link',
                'Date Posted', 'Date Found', 'Job Link', 'External Link',
                'Status', 'Suitability Score', 'Priority', 'Notes',
                'Applied Date', 'Application Status'
            ]
            
            with open(self.csv_file, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                
                for job in self.data["jobs"]:
                    writer.writerow({
                        'Job ID': job.get('job_id', ''),
                        'Title': job.get('title', ''),
                        'Company': job.get('company', ''),
                        'Work Location': job.get('work_location', ''),
                        'Work Style': job.get('work_style', ''),
                        'Description': job.get('description', ''),
                        'Experience Required': job.get('experience_required', ''),
                        'Skills': job.get('skills', ''),
                        'HR Name': job.get('hr_name', ''),
                        'HR Link': job.get('hr_link', ''),
                        'Date Posted': job.get('date_posted', ''),
                        'Date Found': job.get('date_found', ''),
                        'Job Link': job.get('job_link', ''),
                        'External Link': job.get('external_link', ''),
                        'Status': job.get('status', ''),
                        'Suitability Score': job.get('suitability_score', ''),
                        'Priority': job.get('priority', ''),
                        'Notes': job.get('notes', ''),
                        'Applied Date': job.get('applied_date', ''),
                        'Application Status': job.get('application_status', '')
                    })
            
            return True
            
        except Exception as e:
            print_lg(f"CSV export hatası: {e}")
            return False
    
    def get_statistics(self) -> Dict:
        """İstatistikleri getirir"""
        try:
            # Recalculate statistics
            total = len(self.data["jobs"])
            applied = len([job for job in self.data["jobs"] if job.get("status") == "applied"])
            pending = len([job for job in self.data["jobs"] if job.get("status") == "pending"])
            ignored = len([job for job in self.data["jobs"] if job.get("status") == "ignored"])
            
            # Priority distribution
            high_priority = len([job for job in self.data["jobs"] if job.get("priority") == "high"])
            medium_priority = len([job for job in self.data["jobs"] if job.get("priority") == "medium"])
            low_priority = len([job for job in self.data["jobs"] if job.get("priority") == "low"])
            
            # Score distribution
            high_score = len([job for job in self.data["jobs"] if job.get("suitability_score", 0) >= 0.8])
            medium_score = len([job for job in self.data["jobs"] if 0.6 <= job.get("suitability_score", 0) < 0.8])
            low_score = len([job for job in self.data["jobs"] if job.get("suitability_score", 0) < 0.6])
            
            stats = {
                "total_jobs": total,
                "applied_jobs": applied,
                "pending_jobs": pending,
                "ignored_jobs": ignored,
                "application_rate": (applied / total * 100) if total > 0 else 0,
                "priority_distribution": {
                    "high": high_priority,
                    "medium": medium_priority,
                    "low": low_priority
                },
                "score_distribution": {
                    "high": high_score,
                    "medium": medium_score,
                    "low": low_score
                },
                "average_score": sum(job.get("suitability_score", 0) for job in self.data["jobs"]) / total if total > 0 else 0
            }
            
            # Update stored statistics
            self.data["statistics"].update(stats)
            
            return stats
            
        except Exception as e:
            print_lg(f"İstatistik hesaplanırken hata: {e}")
            return self.data["statistics"]
