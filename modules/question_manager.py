"""
LinkedIn Auto Job Applier - <PERSON><PERSON>u modül soru-cevap sisteminin temel işlevlerini sağlar.
"""

import json
import os
import hashlib
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Union, Literal
from pathlib import Path
import shutil

from modules.helpers import print_lg, critical_error_log

class QuestionManager:
    """Soru-cevap sistemini yöneten ana sınıf"""
    
    def __init__(self, data_file: str = "data/questions_answers.json"):
        self.data_file = Path(data_file)
        self.backup_dir = Path("data/backups")
        self.schema_file = Path("data/schema/question_schema.json")
        
        # Dizinleri oluştur
        self.data_file.parent.mkdir(parents=True, exist_ok=True)
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        self.schema_file.parent.mkdir(parents=True, exist_ok=True)
        
        self.data = self._load_data()
    
    def _load_data(self) -> Dict:
        """<PERSON><PERSON>, <PERSON><PERSON><PERSON> ya<PERSON>ı<PERSON>ı oluşturur"""
        try:
            if self.data_file.exists():
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                print_lg(f"Soru-cevap verileri yüklendi: {len(data.get('questions', {}))} kategori")
                return data
            else:
                return self._create_default_structure()
        except Exception as e:
            print_lg(f"Veri dosyası yüklenirken hata: {e}")
            critical_error_log("Question Manager - Data Loading", e)
            return self._create_default_structure()
    
    def _create_default_structure(self) -> Dict:
        """Varsayılan veri yapısını oluşturur"""
        default_data = {
            "schema_version": "1.0",
            "last_updated": datetime.now().isoformat(),
            "questions": {
                "text_questions": {},
                "textarea_questions": {},
                "single_select_questions": {},
                "multiple_select_questions": {},
                "radio_questions": {},
                "checkbox_questions": {}
            },
            "statistics": {
                "total_questions": 0,
                "answered_questions": 0,
                "pending_questions": 0,
                "success_rate": 0.0
            },
            "settings": {
                "auto_answer_enabled": True,
                "confidence_threshold": 0.8,
                "backup_enabled": True,
                "max_backup_files": 10
            }
        }
        self._save_data(default_data)
        return default_data
    
    def _save_data(self, data: Optional[Dict] = None) -> bool:
        """Veriyi dosyaya kaydeder"""
        try:
            if data is None:
                data = self.data
            
            # Backup oluştur
            if self.data_file.exists() and data.get("settings", {}).get("backup_enabled", True):
                self._create_backup()
            
            # Ana dosyayı kaydet
            data["last_updated"] = datetime.now().isoformat()
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            print_lg("Soru-cevap verileri kaydedildi")
            return True
            
        except Exception as e:
            print_lg(f"Veri kaydedilirken hata: {e}")
            critical_error_log("Question Manager - Data Saving", e)
            return False
    
    def _create_backup(self) -> bool:
        """Mevcut veri dosyasının yedeğini oluşturur"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = self.backup_dir / f"questions_answers_backup_{timestamp}.json"
            shutil.copy2(self.data_file, backup_file)
            
            # Eski yedekleri temizle
            self._cleanup_old_backups()
            
            print_lg(f"Backup oluşturuldu: {backup_file.name}")
            return True
            
        except Exception as e:
            print_lg(f"Backup oluşturulurken hata: {e}")
            return False
    
    def _cleanup_old_backups(self):
        """Eski backup dosyalarını temizler"""
        try:
            max_backups = self.data.get("settings", {}).get("max_backup_files", 10)
            backup_files = sorted(self.backup_dir.glob("questions_answers_backup_*.json"))
            
            if len(backup_files) > max_backups:
                for old_backup in backup_files[:-max_backups]:
                    old_backup.unlink()
                    print_lg(f"Eski backup silindi: {old_backup.name}")
                    
        except Exception as e:
            print_lg(f"Backup temizliği sırasında hata: {e}")
    
    def _generate_question_id(self, question_text: str, question_type: str) -> str:
        """Soru için benzersiz ID oluşturur"""
        # Sorunun hash'ini al ve kısa ID oluştur
        content = f"{question_text.lower().strip()}_{question_type}"
        hash_object = hashlib.md5(content.encode())
        return hash_object.hexdigest()
    
    def add_question(self, 
                    question_text: str, 
                    question_type: Literal["text", "textarea", "single_select", "multiple_select", "radio", "checkbox"],
                    options: Optional[List[str]] = None,
                    company_context: Optional[str] = None,
                    job_context: Optional[str] = None) -> str:
        """Yeni soru ekler"""
        try:
            question_id = self._generate_question_id(question_text, question_type)
            category = f"{question_type}_questions"
            
            # Soru zaten varsa güncelle
            if question_id in self.data["questions"][category]:
                existing = self.data["questions"][category][question_id]
                existing["usage_count"] = existing.get("usage_count", 0) + 1
                existing["updated_at"] = datetime.now().isoformat()
                
                # Kontekst bilgilerini güncelle
                if company_context:
                    if "company_context" not in existing:
                        existing["company_context"] = []
                    if company_context not in existing["company_context"]:
                        existing["company_context"].append(company_context)
                
                if job_context:
                    if "job_context" not in existing:
                        existing["job_context"] = []
                    if job_context not in existing["job_context"]:
                        existing["job_context"].append(job_context)
                
                print_lg(f"Mevcut soru güncellendi: {question_text[:50]}...")
                
            else:
                # Yeni soru oluştur
                question_data = {
                    "id": question_id,
                    "question_text": question_text,
                    "question_type": question_type,
                    "answer": "",
                    "confidence": 0.0,
                    "created_at": datetime.now().isoformat(),
                    "updated_at": datetime.now().isoformat(),
                    "usage_count": 1,
                    "success_count": 0,
                    "company_context": [company_context] if company_context else [],
                    "job_context": [job_context] if job_context else [],
                    "tags": [],
                    "status": "pending"
                }
                
                # Seçenekli sorular için ek alanlar
                if question_type in ["single_select", "multiple_select", "radio", "checkbox"]:
                    question_data["options"] = options or []
                    question_data["selected_options"] = []
                
                self.data["questions"][category][question_id] = question_data
                self.data["statistics"]["total_questions"] += 1
                self.data["statistics"]["pending_questions"] += 1
                
                print_lg(f"Yeni soru eklendi: {question_text[:50]}...")
            
            self._save_data()
            return question_id
            
        except Exception as e:
            print_lg(f"Soru eklenirken hata: {e}")
            critical_error_log("Question Manager - Add Question", e)
            return ""
    
    def get_answer(self, question_text: str, question_type: str) -> Optional[str]:
        """Soru için kaydedilmiş cevabı getirir"""
        try:
            question_id = self._generate_question_id(question_text, question_type)
            category = f"{question_type}_questions"
            
            if question_id in self.data["questions"][category]:
                question = self.data["questions"][category][question_id]
                if question["status"] == "answered" and question["answer"]:
                    confidence = question.get("confidence", 0.0)
                    threshold = self.data["settings"]["confidence_threshold"]
                    
                    if confidence >= threshold:
                        print_lg(f"Kaydedilmiş cevap bulundu: {question['answer'][:30]}... (güven: {confidence})")
                        return question["answer"]
                    else:
                        print_lg(f"Cevap güven seviyesi düşük: {confidence} < {threshold}")
            
            return None
            
        except Exception as e:
            print_lg(f"Cevap getirilirken hata: {e}")
            return None
    
    def update_statistics(self):
        """İstatistikleri günceller"""
        try:
            total = 0
            answered = 0
            pending = 0
            
            for category in self.data["questions"].values():
                for question in category.values():
                    total += 1
                    if question["status"] == "answered":
                        answered += 1
                    elif question["status"] == "pending":
                        pending += 1
            
            self.data["statistics"]["total_questions"] = total
            self.data["statistics"]["answered_questions"] = answered
            self.data["statistics"]["pending_questions"] = pending
            self.data["statistics"]["success_rate"] = (answered / total) if total > 0 else 0.0
            
            self._save_data()
            
        except Exception as e:
            print_lg(f"İstatistik güncellenirken hata: {e}")

    def set_answer(self, question_text: str, question_type: str, answer: str, confidence: float = 1.0) -> bool:
        """Soru için cevap belirler"""
        try:
            question_id = self._generate_question_id(question_text, question_type)
            category = f"{question_type}_questions"

            if question_id in self.data["questions"][category]:
                question = self.data["questions"][category][question_id]
                old_status = question["status"]

                question["answer"] = answer
                question["confidence"] = confidence
                question["status"] = "answered"
                question["updated_at"] = datetime.now().isoformat()

                # İstatistikleri güncelle
                if old_status == "pending":
                    self.data["statistics"]["pending_questions"] -= 1
                    self.data["statistics"]["answered_questions"] += 1

                self._save_data()
                print_lg(f"Cevap kaydedildi: {question_text[:50]}... -> {answer[:30]}...")
                return True
            else:
                print_lg(f"Soru bulunamadı: {question_text[:50]}...")
                return False

        except Exception as e:
            print_lg(f"Cevap kaydedilirken hata: {e}")
            critical_error_log("Question Manager - Set Answer", e)
            return False

    def get_pending_questions(self) -> List[Dict]:
        """Cevaplanmamış soruları getirir"""
        try:
            pending_questions = []

            for category_name, category in self.data["questions"].items():
                for question_id, question in category.items():
                    if question["status"] == "pending":
                        pending_questions.append({
                            "id": question_id,
                            "category": category_name,
                            "question_text": question["question_text"],
                            "question_type": question["question_type"],
                            "options": question.get("options", []),
                            "usage_count": question.get("usage_count", 0),
                            "company_context": question.get("company_context", []),
                            "job_context": question.get("job_context", []),
                            "created_at": question["created_at"]
                        })

            # Kullanım sayısına göre sırala (en çok kullanılan önce)
            pending_questions.sort(key=lambda x: x["usage_count"], reverse=True)
            return pending_questions

        except Exception as e:
            print_lg(f"Bekleyen sorular getirilirken hata: {e}")
            return []

    def mark_success(self, question_text: str, question_type: str) -> bool:
        """Sorunun başarıyla cevaplanıp uygulandığını işaretler"""
        try:
            question_id = self._generate_question_id(question_text, question_type)
            category = f"{question_type}_questions"

            if question_id in self.data["questions"][category]:
                question = self.data["questions"][category][question_id]
                question["success_count"] = question.get("success_count", 0) + 1
                question["updated_at"] = datetime.now().isoformat()

                self._save_data()
                return True

            return False

        except Exception as e:
            print_lg(f"Başarı işaretlenirken hata: {e}")
            return False

    def export_questions(self, file_path: str) -> bool:
        """Soruları dışa aktarır"""
        try:
            export_data = {
                "export_date": datetime.now().isoformat(),
                "questions": self.get_pending_questions(),
                "statistics": self.data["statistics"]
            }

            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)

            print_lg(f"Sorular dışa aktarıldı: {file_path}")
            return True

        except Exception as e:
            print_lg(f"Dışa aktarma hatası: {e}")
            return False

    def get_statistics(self) -> Dict:
        """Güncel istatistikleri getirir"""
        self.update_statistics()
        return self.data["statistics"].copy()

    def clear_old_questions(self, days: int = 30) -> int:
        """Belirtilen günden eski cevaplanmamış soruları temizler"""
        try:
            from datetime import timedelta
            cutoff_date = datetime.now() - timedelta(days=days)
            removed_count = 0

            for category_name, category in self.data["questions"].items():
                to_remove = []
                for question_id, question in category.items():
                    if question["status"] == "pending":
                        created_date = datetime.fromisoformat(question["created_at"])
                        if created_date < cutoff_date and question.get("usage_count", 0) <= 1:
                            to_remove.append(question_id)

                for question_id in to_remove:
                    del category[question_id]
                    removed_count += 1

            if removed_count > 0:
                self._save_data()
                print_lg(f"{removed_count} eski soru temizlendi")

            return removed_count

        except Exception as e:
            print_lg(f"Eski sorular temizlenirken hata: {e}")
            return 0
