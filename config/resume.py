 

from personals import *
import json

###################################################### CONFIGURE YOUR RESUME HERE ######################################################


# # Give an relative path of your default resume to be uploaded. If file in not found, will continue using your previously uploaded resume in LinkedIn.
# default_resume_path = "all resumes/default/resume.pdf"      # (In Development)

'''
YOU DON'T HAVE TO EDIT THIS FILE, IF YOU ADDED YOUR DEFAULT RESUME.
'''


# resume_headline = json({
#     "first_name": first_name,
# })











# # >>>>>>>>>>> RELATED SETTINGS <<<<<<<<<<<

# ## Allow Manual Inputs
# # Should the tool pause before every submit application during easy apply to let you check the information?
# pause_before_submit = True         # True or False, Note: True or False are case-sensitive
# '''
# Note: Will be treated as False if `run_in_background = True`
# '''

# # Should the tool pause if it needs help in answering questions during easy apply?
# # Note: If set as False will answer randomly...
# pause_at_failed_question = True    # True or False, Note: True or False are case-sensitive
# '''
# Note: Will be treated as False if `run_in_background = True`
# '''
# ##

# # Do you want to overwrite previous answers?
# overwrite_previous_answers = False # True or False, Note: True or False are case-sensitive







############################################################################################################
    
############################################################################################################