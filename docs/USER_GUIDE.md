# LinkedIn Auto Job Applier - Kullanıcı Kılavuzu

Bu kılavuz, LinkedIn Auto Job Applier'ın yeni özelliklerini nasıl kullanacağınızı adım adım açıklar.

## 🚀 Başlangıç

### 1. Uygulamayı Başlatma

```bash
# Web arayüzünü başlat
python app.py

# Ana bot uygulamasını çalıştır (ayrı terminal)
python runAiBot.py
```

### 2. Web Arayüzüne Erişim

Tarayıcınızda `http://localhost:5000` adresini açın.

## 📊 Dashboard Kullanımı

### Ana Ekran

Dashboard'da şu bilgileri görebilirsiniz:
- **Toplam İş**: Bulunan toplam iş sayısı
- **Başvurulan**: Başarıyla başvuru yapılan işler
- **Bekleyen**: Hen<PERSON>z başvuru yapılmayan işler
- **Başarı Oranı**: Başvuru başarı yüzdesi

### Filtreleme ve Arama

**Sol Sidebar'da:**
- **Durum Filtreleri**: Tümü, Başvurulan, Bekleyen, Başarısız
- **Tarih Aralığı**: Başlangıç ve bitiş tarihi seçin
- **Şirket Filtresi**: Belirli şirketi seçin
- **Arama**: İş başlığı, şirket veya konum arayın
- **Çalışma Şekli**: Remote, Ofiste, Hibrit

**Kullanım:**
1. İstediğiniz filtreleri seçin
2. "Filtrele" butonuna tıklayın
3. Sonuçlar otomatik olarak güncellenecek

### İş Detayları Görüntüleme

1. İş listesinde "👁️" (göz) ikonuna tıklayın
2. Açılan modal'da detaylı bilgileri görün:
   - İş açıklaması
   - Gerekli deneyim ve yetenekler
   - HR bilgileri
   - Başvuru tarihi
   - Sorulan sorular (varsa)

## ❓ Soru Yönetimi

### Soru Arayüzüne Erişim

`http://localhost:5000/questions` adresini ziyaret edin.

### Cevaplanmamış Soruları Görüntüleme

**Ana ekranda:**
- Tüm cevaplanmamış sorular listelenir
- Her soru için şu bilgiler gösterilir:
  - Soru metni
  - Soru tipi (text, select, vb.)
  - Kullanım sayısı
  - Şirket ve iş bağlamı
  - Seçenekler (varsa)

### Soru Cevaplama

**Text/Textarea Soruları:**
1. Cevap kutusuna yanıtınızı yazın
2. "💾 Kaydet" butonuna tıklayın

**Seçenekli Sorular:**
1. Seçenekler listesinden uygun olanı seçin
2. "💾 Kaydet" butonuna tıklayın

**Çoklu Seçim:**
1. Virgülle ayırarak birden fazla seçenek seçin
2. Örnek: "1,3,5" (1., 3. ve 5. seçenekler)

### Konsol Arayüzü Kullanımı

```bash
python question_interface.py
```

**Komutlar:**
- Cevap girin veya `0` yazarak atlayın
- `Ctrl+C` ile çıkış yapın

### CLI Yönetim Araçları

```bash
# Bekleyen soruları listele
python manage_questions.py --list pending

# İstatistikleri göster
python manage_questions.py --stats

# Soruları dışa aktar
python manage_questions.py --export questions.json --format json

# Etkileşimli mod
python manage_questions.py --interactive

# Eski soruları temizle (30 günden eski)
python manage_questions.py --cleanup 30
```

## 🎯 Easy Apply Olmayan İşler

### Arayüze Erişim

`http://localhost:5000/non-easy-apply` adresini ziyaret edin.

### İş Listesi

**Her iş kartında:**
- **Öncelik Göstergesi**: Renkli nokta (Yeşil=Yüksek, Turuncu=Orta, Kırmızı=Düşük)
- **Uygunluk Skoru**: Yüzde olarak gösterilir
- **İş Bilgileri**: Başlık, şirket, konum, çalışma şekli
- **Deneyim Gereksinimi**: Yıl olarak
- **İş Açıklaması**: Kısaltılmış hali

### Filtreleme

**Filtre Seçenekleri:**
- **Öncelik**: Yüksek, Orta, Düşük
- **Minimum Skor**: Kaydırıcı ile ayarlayın (0-100%)
- **Durum**: Bekleyen, Başvurulan, Göz ardı edilen

### İş İşlemleri

**Her iş için 3 seçenek:**

1. **🔗 Başvur**: 
   - Dış başvuru linki varsa direkt oraya yönlendirir
   - Yoksa LinkedIn sayfasını açar

2. **✅ Başvurdum**:
   - Manuel başvuru yaptığınızda tıklayın
   - İş "başvurulan" olarak işaretlenir

3. **❌ Göz Ardı Et**:
   - İlginizi çekmeyen işler için
   - İş listeden kaldırılır

### Skorlama Sistemi

Skorlama şu kriterlere göre yapılır:
- **Arama Terimleri**: `config/search.py`'deki terimlerle eşleşme
- **Kötü Kelimeler**: İstenmeyen terimler için ceza
- **Deneyim Seviyesi**: Mevcut deneyiminizle uyumluluk
- **Çalışma Şekli**: Tercih ettiğiniz çalışma tarzı
- **Şirket İtibarı**: Büyük teknoloji şirketleri için bonus
- **İlan Tazeliği**: Yeni ilanlar için bonus

## 🔧 Ayarlar ve Özelleştirme

### Soru Sistemi Ayarları

`data/questions_answers.json` dosyasını düzenleyin:

```json
{
  "settings": {
    "auto_answer_enabled": true,      // Otomatik cevaplama açık/kapalı
    "confidence_threshold": 0.8,      // Minimum güven skoru (0-1)
    "backup_enabled": true,           // Otomatik yedekleme
    "max_backup_files": 10           // Maksimum yedek dosya sayısı
  }
}
```

### Easy Apply Olmayan İşler Ayarları

`data/non_easy_apply_jobs.json` dosyasını düzenleyin:

```json
{
  "settings": {
    "auto_score": true,              // Otomatik skorlama
    "min_score_threshold": 0.6,      // Minimum gösterim skoru
    "max_jobs_per_company": 5        // Şirket başına maksimum iş
  }
}
```

### Skorlama Kriterlerini Özelleştirme

`config/search.py` dosyasını güncelleyin:
- `search_terms`: Aradığınız pozisyon terimleri
- `bad_words`: Kaçınmak istediğiniz kelimeler
- `current_experience`: Mevcut deneyim yılınız
- `on_site`: Tercih ettiğiniz çalışma şekli

## 📱 Mobil Kullanım

Web arayüzü mobil uyumludur:
- Telefon ve tablet'te düzgün görüntülenir
- Touch-friendly butonlar
- Responsive tasarım
- Hızlı erişim için bookmark ekleyin

## 🔍 Sorun Giderme

### Sık Karşılaşılan Sorunlar

**1. Sorular Kaydedilmiyor**
- `data/` klasörünün yazma izni olduğunu kontrol edin
- Disk alanının yeterli olduğunu kontrol edin

**2. Web Arayüzü Açılmıyor**
- Port 5000'in başka uygulama tarafından kullanılmadığını kontrol edin
- `python app.py` komutunu tekrar çalıştırın

**3. İşler Görünmüyor**
- `runAiBot.py`'nin çalıştığından emin olun
- CSV dosyalarının `all excels/` klasöründe olduğunu kontrol edin

**4. Skorlar Yanlış Hesaplanıyor**
- `config/search.py` ayarlarınızı kontrol edin
- Skorlama algoritmasını `modules/non_easy_apply_manager.py`'de inceleyin

### Hata Logları

**Konsol Çıktısı**: Ana hatalar konsola yazdırılır
**Critical Errors**: Önemli hatalar `critical_error_log` ile kaydedilir
**Web Server**: Flask development server hataları

### Performans İpuçları

1. **Veri Temizliği**: Eski soruları düzenli temizleyin
2. **Filtre Kullanımı**: Büyük veri setlerinde filtre kullanın
3. **Tarayıcı Cache**: Sayfayı yenilerken Ctrl+F5 kullanın

## 💡 İpuçları ve Püf Noktaları

### Verimli Soru Yönetimi

1. **Toplu Cevaplama**: Benzer soruları bir arada cevaplayın
2. **Şablon Cevaplar**: Sık sorulan sorular için standart cevaplar hazırlayın
3. **Düzenli Kontrol**: Haftada bir bekleyen soruları kontrol edin

### Akıllı İş Filtreleme

1. **Skor Eşiği**: Minimum skoru 60% yapın, kaliteli işleri kaçırmayın
2. **Öncelik Takibi**: Yüksek öncelikli işlere önce başvurun
3. **Durum Güncellemesi**: Başvuru durumlarını güncel tutun

### Zaman Yönetimi

1. **Günlük Rutin**: Her gün 15 dakika soru cevaplama
2. **Haftalık İnceleme**: Hafta sonları iş listesini gözden geçirin
3. **Aylık Temizlik**: Ayda bir eski verileri temizleyin

## 📞 Destek

Sorun yaşadığınızda:
1. Bu kılavuzu tekrar okuyun
2. Hata mesajlarını not alın
3. GitHub Issues'da benzer sorunları arayın
4. Yeni issue açarken detaylı bilgi verin

## 🎯 Gelecek Güncellemeler

Planlanan özellikler:
- WhatsApp/Telegram bildirimleri
- Excel export/import
- Gelişmiş raporlama
- Otomatik başvuru zamanlaması
- AI destekli soru cevaplama
