# Çoklu Dil Desteği - LinkedIn Auto Job Applier

Bu dokümantasyon, LinkedIn Auto Job Applier'a eklenen çoklu dil desteğini açıklar.

## 🌍 Desteklenen Diller

- **Türkçe (tr)** - Varsayılan dil
- **İngilizce (en)** - İkincil dil

## 📁 <PERSON><PERSON><PERSON>

```
├── static/
│   └── js/
│       └── i18n.js                 # Ana çoklu dil sistemi
├── templates/
│   ├── dashboard.html              # Ana dashboard (dil desteği eklendi)
│   ├── questions.html              # Soru yönetimi (dil desteği eklendi)
│   ├── non_easy_apply.html         # Easy Apply olmayan işler (dil desteği eklendi)
│   └── test_i18n.html             # Test sayfası
└── docs/
    └── MULTILINGUAL_SUPPORT.md     # Bu dosya
```

## 🔧 Teknik Detaylar

### i18n.js Sistemi

**Özellikler:**
- Otomatik dil tespiti (tarayıcı diline göre)
- LocalStorage ile dil tercihi kaydetme
- Real-time dil değişikliği
- Event-driven sistem
- Fallback mekanizması

**Kullanım:**
```javascript
// Dil değiştirme
i18n.setLanguage('en');

// Çeviri alma
const translation = i18n.translate('nav.dashboard');

// Mevcut dili öğrenme
const currentLang = i18n.getCurrentLanguage();
```

### HTML Entegrasyonu

**data-i18n Attribute:**
```html
<span data-i18n="nav.dashboard">Dashboard</span>
<input data-i18n="filter.searchPlaceholder" placeholder="Arama...">
<title data-i18n="page.title">Sayfa Başlığı</title>
```

**Dil Seçici:**
```html
<li class="nav-item dropdown">
  <a class="nav-link dropdown-toggle" href="#" id="languageDropdown" role="button" data-bs-toggle="dropdown">
    <i class="bi bi-globe"></i> <span data-i18n="lang.select">Dil Seçin</span>
  </a>
  <ul class="dropdown-menu">
    <li><a class="dropdown-item" href="#" onclick="i18n.setLanguage('tr')">
      <i class="bi bi-flag"></i> <span data-i18n="lang.turkish">Türkçe</span>
    </a></li>
    <li><a class="dropdown-item" href="#" onclick="i18n.setLanguage('en')">
      <i class="bi bi-flag"></i> <span data-i18n="lang.english">English</span>
    </a></li>
  </ul>
</li>
```

## 📝 Çeviri Anahtarları

### Navigasyon
- `nav.dashboard` - Dashboard
- `nav.questions` - Sorular / Questions
- `nav.nonEasyApply` - Easy Apply Olmayan / Non-Easy Apply
- `nav.settings` - Ayarlar / Settings

### İstatistikler
- `stats.totalJobs` - Toplam İş / Total Jobs
- `stats.appliedJobs` - Başvurulan / Applied
- `stats.pendingJobs` - Bekleyen / Pending
- `stats.successRate` - Başarı Oranı / Success Rate

### Filtreler
- `filter.title` - Filtreler / Filters
- `filter.status` - Durum / Status
- `filter.status.all` - Tümü / All
- `filter.status.applied` - Başvurulan / Applied
- `filter.status.pending` - Bekleyen / Pending
- `filter.status.failed` - Başarısız / Failed
- `filter.company` - Şirket / Company
- `filter.workStyle` - Çalışma Şekli / Work Style
- `filter.search` - Arama / Search
- `filter.apply` - Filtrele / Apply Filters

### İş Listesi
- `jobs.title` - İş Başvuruları / Job Applications
- `jobs.loading` - Yükleniyor... / Loading...
- `jobs.noResults` - Sonuç bulunamadı / No results found
- `jobs.viewDetails` - Detayları Görüntüle / View Details
- `jobs.appliedOn` - Başvuru Tarihi / Applied On
- `jobs.location` - Konum / Location

### Butonlar
- `btn.refresh` - Yenile / Refresh
- `btn.export` - Dışa Aktar / Export
- `btn.close` - Kapat / Close
- `btn.settings` - Ayarlar / Settings

### Mesajlar
- `msg.dataUpdated` - Veriler güncellendi / Data updated
- `msg.error` - Bir hata oluştu / An error occurred
- `msg.loading` - Yükleniyor... / Loading...
- `msg.autoRefresh` - Otomatik yenileme: 30 saniye / Auto refresh: 30 seconds

### Dil Seçimi
- `lang.turkish` - Türkçe
- `lang.english` - English
- `lang.select` - Dil Seçin / Select Language

## 🚀 Kullanım

### Web Arayüzünde Dil Değiştirme

1. **Navigation Bar'da:** Sağ üstteki dil dropdown'ından seçim yapın
2. **Otomatik Kaydetme:** Seçiminiz localStorage'da saklanır
3. **Anında Değişim:** Sayfa yenilenmeden tüm metinler güncellenir

### Desteklenen Sayfalar

- **Dashboard:** http://localhost:5001/
- **Sorular:** http://localhost:5001/questions
- **Easy Apply Olmayan İşler:** http://localhost:5001/non-easy-apply
- **Test Sayfası:** http://localhost:5001/test-i18n

## 🧪 Test Etme

### Test Sayfası
```
http://localhost:5001/test-i18n
```

**Test Özellikleri:**
- Dil değişikliği butonları
- Tüm çeviri kategorilerinin örnekleri
- Real-time test sonuçları
- Çeviri sayısı ve element sayısı gösterimi

### Manuel Test
1. Tarayıcıda test sayfasını açın
2. Türkçe/English butonlarını tıklayın
3. Tüm metinlerin değiştiğini kontrol edin
4. Sayfa yenilendikten sonra dil tercihinin korunduğunu kontrol edin

## 🔧 Geliştirici Notları

### Yeni Çeviri Ekleme

1. **i18n.js'de çeviri ekleyin:**
```javascript
tr: {
    'yeni.anahtar': 'Türkçe Metin'
},
en: {
    'yeni.anahtar': 'English Text'
}
```

2. **HTML'de kullanın:**
```html
<span data-i18n="yeni.anahtar">Varsayılan Metin</span>
```

### Yeni Dil Ekleme

1. **translations objesine yeni dil ekleyin:**
```javascript
const translations = {
    tr: { /* Türkçe çeviriler */ },
    en: { /* İngilizce çeviriler */ },
    fr: { /* Fransızca çeviriler */ }
};
```

2. **detectBrowserLanguage fonksiyonunu güncelleyin:**
```javascript
detectBrowserLanguage() {
    const browserLang = navigator.language || navigator.userLanguage;
    if (browserLang.startsWith('tr')) return 'tr';
    if (browserLang.startsWith('fr')) return 'fr';
    return 'en'; // Default
}
```

3. **HTML'de dil seçici butonunu ekleyin:**
```html
<li><a class="dropdown-item" href="#" onclick="i18n.setLanguage('fr')">
  <i class="bi bi-flag"></i> Français
</a></li>
```

### Event Listening

```javascript
// Dil değişikliği dinleme
window.addEventListener('languageChanged', function(event) {
    const newLanguage = event.detail.language;
    console.log('Dil değişti:', newLanguage);
    
    // Özel işlemler yapın
    updateChartLabels(newLanguage);
    updateDateFormatting(newLanguage);
});
```

## 📊 Performans

- **Dosya Boyutu:** ~15KB (minified olmadan)
- **Yükleme Süresi:** <100ms
- **Dil Değişikliği:** <50ms
- **Bellek Kullanımı:** ~2MB

## 🐛 Bilinen Sorunlar

1. **Chart.js Labels:** Grafik etiketleri manuel güncelleme gerektirir
2. **Dynamic Content:** Dinamik olarak eklenen içerik manuel çeviri gerektirir
3. **Date Formatting:** Tarih formatları dil değişikliğinde güncellenmez

## 🔮 Gelecek Geliştirmeler

- [ ] Otomatik çeviri API entegrasyonu
- [ ] Daha fazla dil desteği (Almanca, Fransızca, İspanyolca)
- [ ] Pluralization desteği
- [ ] Context-aware çeviriler
- [ ] RTL dil desteği
- [ ] Çeviri eksikliği uyarıları

## 📞 Destek

Çoklu dil desteği ile ilgili sorunlar için:
1. Test sayfasını kontrol edin: `/test-i18n`
2. Browser console'da hata mesajlarını kontrol edin
3. localStorage'da `dashboard-language` anahtarını kontrol edin
4. i18n.js dosyasının yüklendiğini kontrol edin
