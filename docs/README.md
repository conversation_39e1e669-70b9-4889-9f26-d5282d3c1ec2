# LinkedIn Auto Job Applier - Gelişmiş Özellikler

Bu dokümantasyon, LinkedIn Auto Job Applier projesine eklenen yeni özellikleri açıklar.

## 🆕 Yeni Özellikler

### 1. 🤖 Akıllı Soru-Cevap Sistemi

Uygulama artık karşılaştığı soruları otomatik olarak kaydeder ve gelecekte aynı sorularla karşılaştığında önceden verilen cevapları kullanır.

#### Özellikler:
- ✅ Otomatik soru yakalama ve kaydetme
- ✅ Konsol ve web tabanlı cevap alma arayüzü
- ✅ Akıllı cevap öneri sistemi
- ✅ Soru kategorileri (text, textarea, select, radio, checkbox)
- ✅ Şirket ve iş bağlamı ile soru eşleştirme
- ✅ Güven skoru tabanlı cevap filtreleme

#### Kullanım:

**Konsol Arayüzü:**
```bash
python question_interface.py
```

**Web Arayüzü:**
```
http://localhost:5000/questions
```

**CLI Yönetim:**
```bash
python manage_questions.py --list pending
python manage_questions.py --stats
python manage_questions.py --export questions.json
```

### 2. 📊 Modern Web Dashboard

Yeni modern, responsive web arayüzü ile iş başvurularınızı daha iyi takip edin.

#### Özellikler:
- ✅ Modern Bootstrap 5 tasarımı
- ✅ Real-time istatistikler ve grafikler
- ✅ Gelişmiş filtreleme ve arama
- ✅ İş detayları modal'ı
- ✅ Otomatik veri yenileme (30 saniye)
- ✅ Responsive mobil uyumlu tasarım

#### Erişim:
```
http://localhost:5000/
```

### 3. 🎯 Easy Apply Olmayan İş Yönetimi

Kriterlere uygun ancak Easy Apply özelliği olmayan işleri akıllı skorlama sistemi ile yönetin.

#### Özellikler:
- ✅ Otomatik iş uygunluk skorlaması
- ✅ Öncelik tabanlı sıralama (Yüksek/Orta/Düşük)
- ✅ Manuel başvuru link yönetimi
- ✅ İş durumu takibi (Bekleyen/Başvurulan/Göz ardı edilen)
- ✅ `config/search.py` ayarlarına dayalı akıllı skorlama

#### Erişim:
```
http://localhost:5000/non-easy-apply
```

## 📁 Dosya Yapısı

```
├── modules/
│   ├── question_manager.py          # Soru-cevap yönetimi
│   └── non_easy_apply_manager.py    # Easy Apply olmayan iş yönetimi
├── templates/
│   ├── dashboard.html               # Modern dashboard
│   ├── questions.html               # Soru yönetim arayüzü
│   └── non_easy_apply.html         # Easy Apply olmayan işler
├── data/
│   ├── questions_answers.json       # Soru-cevap veritabanı
│   ├── non_easy_apply_jobs.json    # Easy Apply olmayan işler
│   └── schema/                      # JSON şemaları
├── tests/
│   ├── test_question_manager.py     # Soru yönetimi testleri
│   ├── test_api.py                  # API testleri
│   └── test_integration.py          # Entegrasyon testleri
├── docs/
│   ├── README.md                    # Bu dosya
│   ├── API.md                       # API dokümantasyonu
│   └── USER_GUIDE.md               # Kullanıcı kılavuzu
├── question_interface.py           # Konsol soru arayüzü
├── manage_questions.py             # CLI soru yönetimi
└── test_question_system.py        # Sistem test scripti
```

## 🚀 Kurulum ve Çalıştırma

### Gereksinimler

```bash
pip install google-generativeai  # Gemini AI desteği için
pip install pytest              # Testler için
```

### Web Arayüzünü Başlatma

```bash
python app.py
```

Tarayıcınızda şu adresleri ziyaret edin:
- **Dashboard**: http://localhost:5000/
- **Soru Yönetimi**: http://localhost:5000/questions
- **Easy Apply Olmayan İşler**: http://localhost:5000/non-easy-apply

### Ana Uygulamayı Çalıştırma

```bash
python runAiBot.py
```

## 🧪 Test Çalıştırma

```bash
# Tüm testleri çalıştır
pytest

# Belirli test dosyasını çalıştır
pytest tests/test_question_manager.py

# Kapsamlı test raporu
pytest --cov=modules --cov-report=html

# Sistem entegrasyon testi
python test_question_system.py
```

## 📊 API Endpoints

### Soru Yönetimi API

- `GET /questions/pending` - Cevaplanmamış soruları getir
- `POST /questions/answer` - Soru için cevap kaydet
- `GET /questions/statistics` - Soru istatistikleri
- `GET /questions/export` - Soruları dışa aktar

### Easy Apply Olmayan İşler API

- `GET /non-easy-apply-jobs` - İşleri listele
- `GET /non-easy-apply-jobs/statistics` - İstatistikler
- `PUT /non-easy-apply-jobs/{id}/status` - İş durumunu güncelle
- `GET /non-easy-apply-jobs/pending` - Bekleyen işler

### İş Başvuruları API

- `GET /applied-jobs` - Başvurulan işleri getir
- `GET /job-details/{id}` - İş detaylarını getir
- `GET /job-statistics` - İş istatistikleri

## ⚙️ Konfigürasyon

### Soru-Cevap Sistemi Ayarları

`data/questions_answers.json` dosyasındaki `settings` bölümü:

```json
{
  "settings": {
    "auto_answer_enabled": true,
    "confidence_threshold": 0.8,
    "backup_enabled": true,
    "max_backup_files": 10
  }
}
```

### Easy Apply Olmayan İşler Ayarları

`data/non_easy_apply_jobs.json` dosyasındaki `settings` bölümü:

```json
{
  "settings": {
    "auto_score": true,
    "min_score_threshold": 0.6,
    "max_jobs_per_company": 5
  }
}
```

## 🔧 Özelleştirme

### Skorlama Algoritması

`modules/non_easy_apply_manager.py` dosyasındaki `_calculate_suitability_score` metodunu düzenleyerek skorlama kriterlerini özelleştirebilirsiniz.

### Soru Kategorileri

Yeni soru tipleri eklemek için `modules/question_manager.py` dosyasını güncelleyin.

## 🐛 Sorun Giderme

### Sık Karşılaşılan Sorunlar

1. **Gemini API Hatası**: `config/secrets.py`'de API key'inizi kontrol edin
2. **Veri Dosyası Bulunamadı**: `data/` klasörünün var olduğundan emin olun
3. **Web Arayüzü Açılmıyor**: Port 5000'in kullanımda olmadığını kontrol edin

### Log Dosyaları

- Ana uygulama logları: Konsol çıktısı
- Hata logları: `critical_error_log` fonksiyonu ile kaydedilir
- Web server logları: Flask development server çıktısı

## 📈 Performans

### Optimizasyon İpuçları

1. **Veri Boyutu**: Eski soruları düzenli olarak temizleyin
2. **Web Performansı**: Büyük veri setleri için sayfalama kullanın
3. **API Yanıt Süreleri**: Filtreleme parametrelerini optimize edin

### Monitoring

- Soru-cevap başarı oranını takip edin
- Easy Apply olmayan işlerin skorlama dağılımını kontrol edin
- Web arayüzü kullanım istatistiklerini gözlemleyin

## 🤝 Katkıda Bulunma

1. Yeni özellikler için önce issue açın
2. Test coverage'ı %80'in üzerinde tutun
3. Dokümantasyonu güncelleyin
4. Code review sürecini takip edin

## 📝 Lisans

Bu proje mevcut LinkedIn Auto Job Applier lisansı altında dağıtılmaktadır.

## 🆘 Destek

Sorunlar için:
1. Önce dokümantasyonu kontrol edin
2. GitHub Issues'da arama yapın
3. Yeni issue açın (template'i kullanın)
4. Detaylı hata logları ekleyin
