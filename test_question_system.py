#!/usr/bin/env python3
"""
LinkedIn Auto Job Applier - Soru Sistemi Test Scripti
Bu script soru-cevap sisteminin işlevselliğini test eder.
"""

import sys
import os
from pathlib import Path

# Modül yolunu ekle
sys.path.append(str(Path(__file__).parent))

from modules.question_manager import QuestionManager
from modules.helpers import print_lg

def test_question_manager():
    """QuestionManager sınıfını test eder"""
    print_lg("=== QuestionManager Test Başlıyor ===")
    
    # QuestionManager örneği oluştur
    qm = QuestionManager("data/test_questions.json")
    
    # Test soruları ekle
    test_questions = [
        {
            "text": "How many years of experience do you have with Python?",
            "type": "text",
            "company": "TechCorp",
            "job": "Python Developer"
        },
        {
            "text": "What is your current salary expectation?",
            "type": "text",
            "company": "StartupXYZ",
            "job": "Software Engineer"
        },
        {
            "text": "Are you authorized to work in the US?",
            "type": "single_select",
            "options": ["Yes", "No", "Require Sponsorship"],
            "company": "BigTech",
            "job": "Senior Developer"
        },
        {
            "text": "Please describe your experience with machine learning",
            "type": "textarea",
            "company": "AICompany",
            "job": "ML Engineer"
        },
        {
            "text": "Do you have experience with the following technologies?",
            "type": "multiple_select",
            "options": ["React", "Node.js", "Python", "Docker", "AWS"],
            "company": "WebDev Inc",
            "job": "Full Stack Developer"
        }
    ]
    
    # Soruları ekle
    question_ids = []
    for q in test_questions:
        question_id = qm.add_question(
            q["text"], 
            q["type"], 
            options=q.get("options"),
            company_context=q.get("company"),
            job_context=q.get("job")
        )
        question_ids.append(question_id)
        print_lg(f"Soru eklendi: {q['text'][:50]}... (ID: {question_id})")
    
    # İstatistikleri göster
    stats = qm.get_statistics()
    print_lg(f"\nİstatistikler:")
    print_lg(f"Toplam soru: {stats['total_questions']}")
    print_lg(f"Cevaplanan: {stats['answered_questions']}")
    print_lg(f"Bekleyen: {stats['pending_questions']}")
    print_lg(f"Başarı oranı: %{stats['success_rate']*100:.1f}")
    
    # Bekleyen soruları listele
    pending = qm.get_pending_questions()
    print_lg(f"\nBekleyen sorular: {len(pending)}")
    for i, q in enumerate(pending[:3]):  # İlk 3'ünü göster
        print_lg(f"{i+1}. {q['question_text'][:60]}...")
    
    # Test cevapları ekle
    test_answers = [
        ("How many years of experience do you have with Python?", "text", "5 years"),
        ("What is your current salary expectation?", "text", "$80,000 - $100,000"),
        ("Are you authorized to work in the US?", "single_select", "Yes"),
        ("Please describe your experience with machine learning", "textarea", "I have 3 years of experience with ML, including deep learning, NLP, and computer vision projects."),
        ("Do you have experience with the following technologies?", "multiple_select", "React, Python, Docker, AWS")
    ]
    
    print_lg(f"\n=== Cevaplar Ekleniyor ===")
    for question_text, question_type, answer in test_answers:
        success = qm.set_answer(question_text, question_type, answer, confidence=0.9)
        if success:
            print_lg(f"✓ Cevap eklendi: {question_text[:40]}... -> {answer[:30]}...")
        else:
            print_lg(f"✗ Cevap eklenemedi: {question_text[:40]}...")
    
    # Güncellenmiş istatistikleri göster
    stats = qm.get_statistics()
    print_lg(f"\nGüncellenmiş İstatistikler:")
    print_lg(f"Toplam soru: {stats['total_questions']}")
    print_lg(f"Cevaplanan: {stats['answered_questions']}")
    print_lg(f"Bekleyen: {stats['pending_questions']}")
    print_lg(f"Başarı oranı: %{stats['success_rate']*100:.1f}")
    
    # Cevap alma testleri
    print_lg(f"\n=== Cevap Alma Testleri ===")
    for question_text, question_type, expected_answer in test_answers:
        retrieved_answer = qm.get_answer(question_text, question_type)
        if retrieved_answer:
            print_lg(f"✓ Cevap bulundu: {question_text[:40]}... -> {retrieved_answer[:30]}...")
            # Başarı işaretle
            qm.mark_success(question_text, question_type)
        else:
            print_lg(f"✗ Cevap bulunamadı: {question_text[:40]}...")
    
    # Export testi
    print_lg(f"\n=== Export Testi ===")
    export_success = qm.export_questions("data/test_export.json")
    if export_success:
        print_lg("✓ Export başarılı")
    else:
        print_lg("✗ Export başarısız")
    
    print_lg("=== Test Tamamlandı ===")

def test_integration():
    """Entegrasyon testleri"""
    print_lg("\n=== Entegrasyon Testleri ===")
    
    # Gerçek soru-cevap dosyasını test et
    qm = QuestionManager()
    
    # Örnek sorular ekle (gerçek LinkedIn sorularına benzer)
    real_questions = [
        "How many years of experience do you have?",
        "What is your current location?",
        "Are you willing to relocate?",
        "What is your expected salary?",
        "Do you have a valid work permit?",
        "Please provide a brief summary of your experience"
    ]
    
    for question in real_questions:
        question_type = "textarea" if "summary" in question.lower() else "text"
        qm.add_question(question, question_type)
    
    # İstatistikleri göster
    stats = qm.get_statistics()
    print_lg(f"Gerçek sistem istatistikleri:")
    print_lg(f"Toplam soru: {stats['total_questions']}")
    print_lg(f"Cevaplanan: {stats['answered_questions']}")
    print_lg(f"Bekleyen: {stats['pending_questions']}")
    
    # Bekleyen soruları göster
    pending = qm.get_pending_questions()
    if pending:
        print_lg(f"\nBekleyen sorular ({len(pending)} adet):")
        for i, q in enumerate(pending[:5]):  # İlk 5'ini göster
            print_lg(f"{i+1}. {q['question_text']}")
            print_lg(f"   Tip: {q['question_type']}, Kullanım: {q['usage_count']}")
    else:
        print_lg("Bekleyen soru yok!")

def cleanup_test_files():
    """Test dosyalarını temizle"""
    test_files = [
        "data/test_questions.json",
        "data/test_export.json"
    ]
    
    for file_path in test_files:
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                print_lg(f"Test dosyası silindi: {file_path}")
        except Exception as e:
            print_lg(f"Test dosyası silinemedi {file_path}: {e}")

def main():
    """Ana test fonksiyonu"""
    print_lg("LinkedIn Auto Job Applier - Soru Sistemi Test Başlıyor")
    print_lg("=" * 60)
    
    try:
        # Test dosyalarını temizle
        cleanup_test_files()
        
        # QuestionManager testleri
        test_question_manager()
        
        # Entegrasyon testleri
        test_integration()
        
        print_lg("\n" + "=" * 60)
        print_lg("Tüm testler başarıyla tamamlandı!")
        
    except Exception as e:
        print_lg(f"Test sırasında hata: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Test dosyalarını temizle
        cleanup_test_files()

if __name__ == "__main__":
    main()
