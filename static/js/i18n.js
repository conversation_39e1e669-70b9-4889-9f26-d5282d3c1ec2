/**
 * Internationalization (i18n) support for LinkedIn Auto Job Applier
 * Supports Turkish (tr) and English (en)
 */

const translations = {
  tr: {
    // Navigation
    "nav.dashboard": "Dashboard",
    "nav.questions": "<PERSON><PERSON><PERSON>",
    "nav.nonEasyApply": "Easy Apply Olmayan",
    "nav.settings": "Ayarlar",

    // Page titles
    "page.title": "LinkedIn Auto Job Applier Dashboard",
    "page.subtitle": "İş başvurularınızı takip edin ve yönetin",

    // Statistics cards
    "stats.totalJobs": "Toplam İş",
    "stats.appliedJobs": "Başvurulan",
    "stats.pendingJobs": "Bekleyen",
    "stats.successRate": "Başarı Oranı",
    "stats.todayApplications": "Bugünkü Başvurular",
    "stats.thisWeek": "Bu Hafta",
    "stats.thisMonth": "Bu Ay",
    "stats.averagePerDay": "Günlük Ortalama",

    // Filters
    "filter.title": "Filtreler",
    "filter.status": "Durum",
    "filter.status.all": "Tümü",
    "filter.status.applied": "Başvurulan",
    "filter.status.pending": "Bekleyen",
    "filter.status.failed": "Başarısız",
    "filter.dateRange": "Tarih Aralığı",
    "filter.dateFrom": "Başlangıç",
    "filter.dateTo": "Bitiş",
    "filter.company": "Şirket",
    "filter.company.all": "Tüm Şirketler",
    "filter.workStyle": "Çalışma Şekli",
    "filter.workStyle.all": "Tümü",
    "filter.workStyle.remote": "Remote",
    "filter.workStyle.onsite": "Ofiste",
    "filter.workStyle.hybrid": "Hibrit",
    "filter.search": "Arama",
    "filter.searchPlaceholder": "İş başlığı, şirket veya konum ara...",
    "filter.apply": "Filtrele",
    "filter.reset": "Sıfırla",

    // Job list
    "jobs.title": "İş Başvuruları",
    "jobs.noResults": "Sonuç bulunamadı",
    "jobs.noResultsDesc":
      "Seçilen kriterlere uygun iş bulunamadı. Filtreleri değiştirmeyi deneyin.",
    "jobs.loading": "Yükleniyor...",
    "jobs.viewDetails": "Detayları Görüntüle",
    "jobs.applied": "Başvuruldu",
    "jobs.pending": "Bekliyor",
    "jobs.failed": "Başarısız",
    "jobs.appliedOn": "Başvuru Tarihi",
    "jobs.postedOn": "İlan Tarihi",
    "jobs.location": "Konum",
    "jobs.workStyle": "Çalışma Şekli",
    "jobs.experience": "Deneyim",
    "jobs.skills": "Yetenekler",
    "jobs.hrContact": "İK İletişim",
    "jobs.questions": "Sorular",
    "jobs.resume": "Özgeçmiş",

    // Charts
    "chart.dailyApplications": "Günlük Başvurular",
    "chart.statusDistribution": "Durum Dağılımı",
    "chart.applications": "Başvurular",
    "chart.noData": "Veri bulunamadı",

    // Buttons
    "btn.close": "Kapat",
    "btn.refresh": "Yenile",
    "btn.export": "Dışa Aktar",
    "btn.settings": "Ayarlar",

    // Messages
    "msg.dataUpdated": "Veriler güncellendi",
    "msg.error": "Bir hata oluştu",
    "msg.loading": "Yükleniyor...",
    "msg.noConnection": "Bağlantı hatası",
    "msg.autoRefresh": "Otomatik yenileme: 30 saniye",

    // Language
    "lang.turkish": "Türkçe",
    "lang.english": "English",
    "lang.select": "Dil Seçin",

    // Questions page
    "questions.title": "Soru Cevap Sistemi",
    "questions.subtitle": "Cevaplanmamış soruları görüntüleyin ve yanıtlayın",
    "questions.pending": "Bekleyen Sorular",
    "questions.answered": "Cevaplanan Sorular",
    "questions.statistics": "İstatistikler",
    "questions.totalQuestions": "Toplam Soru",
    "questions.pendingQuestions": "Bekleyen",
    "questions.answeredQuestions": "Cevaplanan",
    "questions.successRate": "Başarı Oranı",
    "questions.answer": "Cevapla",
    "questions.save": "Kaydet",
    "questions.skip": "Atla",
    "questions.questionText": "Soru Metni",
    "questions.questionType": "Soru Tipi",
    "questions.usageCount": "Kullanım Sayısı",
    "questions.company": "Şirket",
    "questions.jobContext": "İş Bağlamı",
    "questions.confidence": "Güven Skoru",
    "questions.yourAnswer": "Cevabınız",
    "questions.placeholder": "Cevabınızı buraya yazın...",

    // Non-easy-apply page
    "nonEasyApply.title": "Easy Apply Olmayan İşler",
    "nonEasyApply.subtitle":
      "Kriterlere uygun ancak Easy Apply özelliği olmayan işleri öncelik sırasına göre görüntüleyin",
    "nonEasyApply.totalJobs": "Toplam İş",
    "nonEasyApply.pendingJobs": "Bekleyen",
    "nonEasyApply.appliedJobs": "Başvurulan",
    "nonEasyApply.averageScore": "Ortalama Skor",
    "nonEasyApply.priority": "Öncelik",
    "nonEasyApply.priority.high": "Yüksek",
    "nonEasyApply.priority.medium": "Orta",
    "nonEasyApply.priority.low": "Düşük",
    "nonEasyApply.minimumScore": "Minimum Skor",
    "nonEasyApply.status": "Durum",
    "nonEasyApply.status.pending": "Bekleyen",
    "nonEasyApply.status.applied": "Başvurulan",
    "nonEasyApply.status.ignored": "Göz ardı edilen",
    "nonEasyApply.apply": "Başvur",
    "nonEasyApply.applied": "Başvurdum",
    "nonEasyApply.ignore": "Göz Ardı Et",
    "nonEasyApply.suitability": "Uygunluk",
    "nonEasyApply.experience": "Deneyim",
    "nonEasyApply.years": "yıl",
    "nonEasyApply.noJobs": "İş bulunamadı",
    "nonEasyApply.noJobsDesc": "Seçilen kriterlere uygun iş bulunmuyor.",
  },

  en: {
    // Navigation
    "nav.dashboard": "Dashboard",
    "nav.questions": "Questions",
    "nav.nonEasyApply": "Non-Easy Apply",
    "nav.settings": "Settings",

    // Page titles
    "page.title": "LinkedIn Auto Job Applier Dashboard",
    "page.subtitle": "Track and manage your job applications",

    // Statistics cards
    "stats.totalJobs": "Total Jobs",
    "stats.appliedJobs": "Applied",
    "stats.pendingJobs": "Pending",
    "stats.successRate": "Success Rate",
    "stats.todayApplications": "Today's Applications",
    "stats.thisWeek": "This Week",
    "stats.thisMonth": "This Month",
    "stats.averagePerDay": "Average Per Day",

    // Filters
    "filter.title": "Filters",
    "filter.status": "Status",
    "filter.status.all": "All",
    "filter.status.applied": "Applied",
    "filter.status.pending": "Pending",
    "filter.status.failed": "Failed",
    "filter.dateRange": "Date Range",
    "filter.dateFrom": "From",
    "filter.dateTo": "To",
    "filter.company": "Company",
    "filter.company.all": "All Companies",
    "filter.workStyle": "Work Style",
    "filter.workStyle.all": "All",
    "filter.workStyle.remote": "Remote",
    "filter.workStyle.onsite": "On-site",
    "filter.workStyle.hybrid": "Hybrid",
    "filter.search": "Search",
    "filter.searchPlaceholder": "Search job title, company or location...",
    "filter.apply": "Apply Filters",
    "filter.reset": "Reset",

    // Job list
    "jobs.title": "Job Applications",
    "jobs.noResults": "No results found",
    "jobs.noResultsDesc":
      "No jobs found matching the selected criteria. Try adjusting your filters.",
    "jobs.loading": "Loading...",
    "jobs.viewDetails": "View Details",
    "jobs.applied": "Applied",
    "jobs.pending": "Pending",
    "jobs.failed": "Failed",
    "jobs.appliedOn": "Applied On",
    "jobs.postedOn": "Posted On",
    "jobs.location": "Location",
    "jobs.workStyle": "Work Style",
    "jobs.experience": "Experience",
    "jobs.skills": "Skills",
    "jobs.hrContact": "HR Contact",
    "jobs.questions": "Questions",
    "jobs.resume": "Resume",

    // Charts
    "chart.dailyApplications": "Daily Applications",
    "chart.statusDistribution": "Status Distribution",
    "chart.applications": "Applications",
    "chart.noData": "No data available",

    // Buttons
    "btn.close": "Close",
    "btn.refresh": "Refresh",
    "btn.export": "Export",
    "btn.settings": "Settings",

    // Messages
    "msg.dataUpdated": "Data updated",
    "msg.error": "An error occurred",
    "msg.loading": "Loading...",
    "msg.noConnection": "Connection error",
    "msg.autoRefresh": "Auto refresh: 30 seconds",

    // Language
    "lang.turkish": "Türkçe",
    "lang.english": "English",
    "lang.select": "Select Language",

    // Questions page
    "questions.title": "Question Answer System",
    "questions.subtitle": "View and answer unanswered questions",
    "questions.pending": "Pending Questions",
    "questions.answered": "Answered Questions",
    "questions.statistics": "Statistics",
    "questions.totalQuestions": "Total Questions",
    "questions.pendingQuestions": "Pending",
    "questions.answeredQuestions": "Answered",
    "questions.successRate": "Success Rate",
    "questions.answer": "Answer",
    "questions.save": "Save",
    "questions.skip": "Skip",
    "questions.questionText": "Question Text",
    "questions.questionType": "Question Type",
    "questions.usageCount": "Usage Count",
    "questions.company": "Company",
    "questions.jobContext": "Job Context",
    "questions.confidence": "Confidence Score",
    "questions.yourAnswer": "Your Answer",
    "questions.placeholder": "Type your answer here...",

    // Non-easy-apply page
    "nonEasyApply.title": "Non-Easy Apply Jobs",
    "nonEasyApply.subtitle":
      "View jobs that match criteria but don't have Easy Apply feature, sorted by priority",
    "nonEasyApply.totalJobs": "Total Jobs",
    "nonEasyApply.pendingJobs": "Pending",
    "nonEasyApply.appliedJobs": "Applied",
    "nonEasyApply.averageScore": "Average Score",
    "nonEasyApply.priority": "Priority",
    "nonEasyApply.priority.high": "High",
    "nonEasyApply.priority.medium": "Medium",
    "nonEasyApply.priority.low": "Low",
    "nonEasyApply.minimumScore": "Minimum Score",
    "nonEasyApply.status": "Status",
    "nonEasyApply.status.pending": "Pending",
    "nonEasyApply.status.applied": "Applied",
    "nonEasyApply.status.ignored": "Ignored",
    "nonEasyApply.apply": "Apply",
    "nonEasyApply.applied": "Applied",
    "nonEasyApply.ignore": "Ignore",
    "nonEasyApply.suitability": "Suitability",
    "nonEasyApply.experience": "Experience",
    "nonEasyApply.years": "years",
    "nonEasyApply.noJobs": "No jobs found",
    "nonEasyApply.noJobsDesc": "No jobs found matching the selected criteria.",
  },
};

class I18n {
  constructor() {
    this.currentLanguage =
      this.getStoredLanguage() || this.detectBrowserLanguage();
    this.init();
  }

  init() {
    this.applyTranslations();
    this.updateLanguageSelector();
  }

  getStoredLanguage() {
    return localStorage.getItem("dashboard-language");
  }

  detectBrowserLanguage() {
    const browserLang = navigator.language || navigator.userLanguage;
    if (browserLang.startsWith("tr")) {
      return "tr";
    }
    return "en"; // Default to English
  }

  setLanguage(lang) {
    if (translations[lang]) {
      this.currentLanguage = lang;
      localStorage.setItem("dashboard-language", lang);
      this.applyTranslations();
      this.updateLanguageSelector();

      // Update document language
      document.documentElement.lang = lang;

      // Trigger custom event for other components
      window.dispatchEvent(
        new CustomEvent("languageChanged", { detail: { language: lang } })
      );
    }
  }

  translate(key, defaultValue = key) {
    const translation = translations[this.currentLanguage];
    return translation && translation[key] ? translation[key] : defaultValue;
  }

  applyTranslations() {
    // Update all elements with data-i18n attribute
    document.querySelectorAll("[data-i18n]").forEach((element) => {
      const key = element.getAttribute("data-i18n");
      const translation = this.translate(key);

      if (
        element.tagName === "INPUT" &&
        (element.type === "text" || element.type === "search")
      ) {
        element.placeholder = translation;
      } else {
        element.textContent = translation;
      }
    });

    // Update title
    const titleKey = document.querySelector("title").getAttribute("data-i18n");
    if (titleKey) {
      document.title = this.translate(titleKey);
    }
  }

  updateLanguageSelector() {
    const selector = document.getElementById("languageSelector");
    if (selector) {
      selector.value = this.currentLanguage;
    }
  }

  getCurrentLanguage() {
    return this.currentLanguage;
  }
}

// Global i18n instance
let i18n;

// Initialize when DOM is loaded
document.addEventListener("DOMContentLoaded", function () {
  i18n = new I18n();

  // Setup language selector
  const languageSelector = document.getElementById("languageSelector");
  if (languageSelector) {
    languageSelector.addEventListener("change", function () {
      i18n.setLanguage(this.value);
    });
  }
});

// Export for use in other scripts
if (typeof window !== "undefined") {
  window.i18n = i18n;
}
