from flask import Flask, request, jsonify, render_template, send_from_directory
from flask_cors import CORS
import csv
from datetime import datetime
import os
import sys
from pathlib import Path

# Modül yolunu ekle
sys.path.append(str(Path(__file__).parent))
from modules.question_manager import QuestionManager
from modules.non_easy_apply_manager import NonEasyApplyManager

app = Flask(__name__)
CORS(app)

PATH = 'all excels/'
# Soru yönetim sistemi
question_manager = QuestionManager()
# Easy Apply olmayan iş yönetim sistemi
non_easy_apply_manager = NonEasyApplyManager()
##> ------ Ka<PERSON><PERSON>e : <EMAIL> - UI for excel files ------
@app.route('/')
def home():
    """Displays the modern dashboard page."""
    return render_template('dashboard.html')

@app.route('/legacy')
def legacy():
    """Displays the legacy index page."""
    return render_template('index.html')

@app.route('/questions')
def questions():
    """Displays the questions management page."""
    return render_template('questions.html')

@app.route('/dashboard')
def dashboard():
    """Displays the modern dashboard page."""
    return render_template('dashboard.html')

@app.route('/non-easy-apply')
def non_easy_apply():
    """Displays the non-easy-apply jobs page."""
    return render_template('non_easy_apply.html')

@app.route('/static/<path:filename>')
def static_files(filename):
    """Serve static files"""
    return send_from_directory('static', filename)

@app.route('/test-i18n')
def test_i18n():
    """Test page for internationalization"""
    return render_template('test_i18n.html')

@app.route('/applied-jobs', methods=['GET'])
def get_applied_jobs():
    '''
    Retrieves a list of applied jobs from the applications history CSV file.

    Returns a JSON response containing a list of jobs with comprehensive details.
    '''
    try:
        jobs = []
        with open(PATH + 'all_applied_applications_history.csv', 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            for row in reader:
                jobs.append({
                    'Job_ID': row.get('Job ID', ''),
                    'Title': row.get('Title', ''),
                    'Company': row.get('Company', ''),
                    'Work_Location': row.get('Work Location', ''),
                    'Work_Style': row.get('Work Style', ''),
                    'About_Job': row.get('About Job', ''),
                    'Experience_Required': row.get('Experience required', ''),
                    'Skills_Required': row.get('Skills required', ''),
                    'HR_Name': row.get('HR Name', ''),
                    'HR_Link': row.get('HR Link', ''),
                    'Resume': row.get('Resume', ''),
                    'Reposted': row.get('Re-posted', ''),
                    'Date_Posted': row.get('Date Posted', ''),
                    'Date_Applied': row.get('Date Applied', ''),
                    'Job_Link': row.get('Job Link', ''),
                    'External_Job_link': row.get('External Job link', ''),
                    'Questions_Found': row.get('Questions Found', ''),
                    'Connect_Request': row.get('Connect Request', '')
                })
        return jsonify(jobs)
    except FileNotFoundError:
        return jsonify({"error": "No applications history found"}), 404
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/job-details/<job_id>', methods=['GET'])
def get_job_details(job_id):
    '''
    Retrieves detailed information for a specific job by ID.
    '''
    try:
        with open(PATH + 'all_applied_applications_history.csv', 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            for row in reader:
                if row.get('Job ID') == job_id:
                    job_details = {
                        'Job_ID': row.get('Job ID', ''),
                        'Title': row.get('Title', ''),
                        'Company': row.get('Company', ''),
                        'Work_Location': row.get('Work Location', ''),
                        'Work_Style': row.get('Work Style', ''),
                        'About_Job': row.get('About Job', ''),
                        'Experience_Required': row.get('Experience required', ''),
                        'Skills_Required': row.get('Skills required', ''),
                        'HR_Name': row.get('HR Name', ''),
                        'HR_Link': row.get('HR Link', ''),
                        'Resume': row.get('Resume', ''),
                        'Reposted': row.get('Re-posted', ''),
                        'Date_Posted': row.get('Date Posted', ''),
                        'Date_Applied': row.get('Date Applied', ''),
                        'Job_Link': row.get('Job Link', ''),
                        'External_Job_link': row.get('External Job link', ''),
                        'Questions_Found': row.get('Questions Found', ''),
                        'Connect_Request': row.get('Connect Request', '')
                    }
                    return jsonify(job_details)

        return jsonify({"error": "Job not found"}), 404
    except FileNotFoundError:
        return jsonify({"error": "No applications history found"}), 404
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/job-statistics', methods=['GET'])
@app.route('/api/statistics', methods=['GET'])
def get_job_statistics():
    '''
    Retrieves statistics about job applications.
    '''
    try:
        jobs = []
        with open(PATH + 'all_applied_applications_history.csv', 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            jobs = list(reader)

        total_jobs = len(jobs)
        applied_jobs = len([job for job in jobs if job.get('Date Applied') and job.get('Date Applied') != 'Pending'])
        pending_jobs = len([job for job in jobs if not job.get('Date Applied') or job.get('Date Applied') == 'Pending'])

        # Company statistics
        companies = {}
        for job in jobs:
            company = job.get('Company', 'Unknown')
            companies[company] = companies.get(company, 0) + 1

        # Daily statistics (last 30 days)
        from datetime import datetime, timedelta
        daily_stats = {}
        cutoff_date = datetime.now() - timedelta(days=30)

        for job in jobs:
            date_applied = job.get('Date Applied')
            if date_applied and date_applied != 'Pending':
                try:
                    job_date = datetime.strptime(date_applied, '%Y-%m-%d %H:%M:%S')
                    if job_date >= cutoff_date:
                        date_key = job_date.strftime('%Y-%m-%d')
                        daily_stats[date_key] = daily_stats.get(date_key, 0) + 1
                except:
                    pass

        statistics = {
            'total_jobs': total_jobs,
            'applied_jobs': applied_jobs,
            'pending_jobs': pending_jobs,
            'success_rate': (applied_jobs / total_jobs * 100) if total_jobs > 0 else 0,
            'top_companies': sorted(companies.items(), key=lambda x: x[1], reverse=True)[:10],
            'daily_applications': daily_stats
        }

        return jsonify(statistics)
    except FileNotFoundError:
        return jsonify({"error": "No applications history found"}), 404
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/applied-jobs/<job_id>', methods=['PUT'])
def update_applied_date(job_id):
    """
    Updates the 'Date Applied' field of a job in the applications history CSV file.

    Args:
        job_id (str): The Job ID of the job to be updated.

    Returns:
        A JSON response with a message indicating success or failure of the update
        operation. If the job is not found, returns a 404 error with a relevant
        message. If any other exception occurs, returns a 500 error with the
        exception message.
    """
    try:
        data = []
        csvPath = PATH + 'all_applied_applications_history.csv'
        
        if not os.path.exists(csvPath):
            return jsonify({"error": f"CSV file not found at {csvPath}"}), 404
            
        # Read current CSV content
        with open(csvPath, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            fieldNames = reader.fieldnames
            found = False
            for row in reader:
                if row['Job ID'] == job_id:
                    row['Date Applied'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    found = True
                data.append(row)
        
        if not found:
            return jsonify({"error": f"Job ID {job_id} not found"}), 404

        with open(csvPath, 'w', encoding='utf-8', newline='') as file:
            writer = csv.DictWriter(file, fieldnames=fieldNames)
            writer.writeheader()
            writer.writerows(data)
        
        return jsonify({"message": "Date Applied updated successfully"}), 200
    except Exception as e:
        print(f"Error updating applied date: {str(e)}")  # Debug log
        return jsonify({"error": str(e)}), 500

@app.route('/questions/pending', methods=['GET'])
def get_pending_questions():
    """Cevaplanmamış soruları getirir"""
    try:
        pending_questions = question_manager.get_pending_questions()
        return jsonify({
            "questions": pending_questions,
            "total_count": len(pending_questions)
        })
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/questions/answer', methods=['POST'])
def answer_question():
    """Soru için cevap kaydeder"""
    try:
        data = request.get_json()
        question_text = data.get('question_text')
        question_type = data.get('question_type')
        answer = data.get('answer')
        confidence = data.get('confidence', 1.0)

        if not all([question_text, question_type, answer]):
            return jsonify({"error": "Missing required fields"}), 400

        success = question_manager.set_answer(question_text, question_type, answer, confidence)

        if success:
            return jsonify({"message": "Answer saved successfully"})
        else:
            return jsonify({"error": "Failed to save answer"}), 500

    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/questions/statistics', methods=['GET'])
@app.route('/api/questions/statistics', methods=['GET'])
def get_question_statistics():
    """Soru istatistiklerini getirir"""
    try:
        stats = question_manager.get_statistics()
        return jsonify(stats)
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/questions/export', methods=['GET'])
def export_questions():
    """Soruları dışa aktarır"""
    try:
        file_path = "data/exported_questions.json"
        success = question_manager.export_questions(file_path)

        if success:
            return jsonify({"message": f"Questions exported to {file_path}"})
        else:
            return jsonify({"error": "Export failed"}), 500

    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/non-easy-apply-jobs', methods=['GET'])
def get_non_easy_apply_jobs():
    """Easy Apply olmayan işleri getirir"""
    try:
        priority = request.args.get('priority')
        limit = request.args.get('limit', type=int)
        min_score = request.args.get('min_score', type=float)

        if priority:
            jobs = non_easy_apply_manager.get_jobs_by_priority(priority, limit)
        else:
            jobs = non_easy_apply_manager.get_pending_jobs(min_score)
            if limit:
                jobs = jobs[:limit]

        return jsonify({
            "jobs": jobs,
            "total_count": len(jobs)
        })
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/non-easy-apply-jobs/statistics', methods=['GET'])
@app.route('/api/non-easy-apply/statistics', methods=['GET'])
def get_non_easy_apply_statistics():
    """Easy Apply olmayan işlerin istatistiklerini getirir"""
    try:
        stats = non_easy_apply_manager.get_statistics()
        return jsonify(stats)
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/non-easy-apply-jobs/<job_id>/status', methods=['PUT'])
def update_non_easy_apply_job_status(job_id):
    """Easy Apply olmayan işin durumunu günceller"""
    try:
        data = request.get_json()
        status = data.get('status')
        notes = data.get('notes', '')

        if not status:
            return jsonify({"error": "Status is required"}), 400

        success = non_easy_apply_manager.update_job_status(job_id, status, notes)

        if success:
            return jsonify({"message": "Job status updated successfully"})
        else:
            return jsonify({"error": "Job not found"}), 404

    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/non-easy-apply-jobs/pending', methods=['GET'])
def get_pending_non_easy_apply_jobs():
    """Bekleyen Easy Apply olmayan işleri getirir"""
    try:
        min_score = request.args.get('min_score', 0.6, type=float)
        limit = request.args.get('limit', type=int)

        jobs = non_easy_apply_manager.get_pending_jobs(min_score)

        if limit:
            jobs = jobs[:limit]

        return jsonify({
            "jobs": jobs,
            "total_count": len(jobs)
        })
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/non-easy-apply-jobs/export', methods=['GET'])
def export_non_easy_apply_jobs():
    """Easy Apply olmayan işleri CSV olarak dışa aktarır"""
    try:
        success = non_easy_apply_manager._export_to_csv()

        if success:
            return jsonify({
                "message": "Jobs exported successfully",
                "file_path": str(non_easy_apply_manager.csv_file)
            })
        else:
            return jsonify({"error": "Export failed"}), 500

    except Exception as e:
        return jsonify({"error": str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True, port=5001)

##<