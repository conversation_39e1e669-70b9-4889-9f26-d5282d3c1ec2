#!/usr/bin/env python3
"""
LinkedIn Auto Job Applier - <PERSON>ru Cevap Arayüzü
Bu script cevaplanmamış soruları gösterir ve kullanıcıdan cevap alır.
"""

import sys
import os
from pathlib import Path
from typing import List, Dict, Optional
import json

# Mod<PERSON>l yolunu ekle
sys.path.append(str(Path(__file__).parent))

from modules.question_manager import QuestionManager
from modules.helpers import print_lg

class QuestionInterface:
    """Soru-cevap arayüzü sınıfı"""
    
    def __init__(self):
        self.question_manager = QuestionManager()
        self.pending_questions = []
        
    def load_pending_questions(self) -> bool:
        """Cevaplanmamış soruları yükler"""
        try:
            self.pending_questions = self.question_manager.get_pending_questions()
            print_lg(f"{len(self.pending_questions)} cevaplanmamış soru bulundu")
            return True
        except Exception as e:
            print_lg(f"So<PERSON><PERSON> yüklenirken hata: {e}")
            return False
    
    def display_question(self, question: Dict, index: int) -> None:
        """Soruyu konsola yazdırır"""
        print("\n" + "="*80)
        print(f"SORU {index + 1}/{len(self.pending_questions)}")
        print("="*80)
        print(f"Soru Metni: {question['question_text']}")
        print(f"Soru Tipi: {question['question_type']}")
        print(f"Kullanım Sayısı: {question['usage_count']}")
        
        if question.get('options'):
            print(f"Seçenekler:")
            for i, option in enumerate(question['options'], 1):
                print(f"  {i}. {option}")
        
        if question.get('company_context'):
            print(f"Şirket Bağlamı: {', '.join(question['company_context'])}")
        
        if question.get('job_context'):
            print(f"İş Bağlamı: {', '.join(question['job_context'])}")
        
        print("-"*80)
    
    def get_user_answer(self, question: Dict) -> Optional[str]:
        """Kullanıcıdan cevap alır"""
        question_type = question['question_type']
        
        while True:
            if question_type in ['single_select', 'radio'] and question.get('options'):
                print("\nSeçenekler:")
                for i, option in enumerate(question['options'], 1):
                    print(f"  {i}. {option}")
                
                try:
                    choice = input("\nSeçenek numarası girin (0=atla): ").strip()
                    if choice == '0':
                        return None
                    
                    choice_idx = int(choice) - 1
                    if 0 <= choice_idx < len(question['options']):
                        return question['options'][choice_idx]
                    else:
                        print("Geçersiz seçenek numarası!")
                        continue
                except ValueError:
                    print("Lütfen geçerli bir sayı girin!")
                    continue
            
            elif question_type in ['multiple_select', 'checkbox'] and question.get('options'):
                print("\nSeçenekler (virgülle ayırarak birden fazla seçebilirsiniz):")
                for i, option in enumerate(question['options'], 1):
                    print(f"  {i}. {option}")
                
                try:
                    choices = input("\nSeçenek numaralarını girin (örn: 1,3,5) (0=atla): ").strip()
                    if choices == '0':
                        return None
                    
                    selected_options = []
                    for choice in choices.split(','):
                        choice_idx = int(choice.strip()) - 1
                        if 0 <= choice_idx < len(question['options']):
                            selected_options.append(question['options'][choice_idx])
                    
                    if selected_options:
                        return ', '.join(selected_options)
                    else:
                        print("Geçersiz seçenekler!")
                        continue
                except ValueError:
                    print("Lütfen geçerli sayılar girin!")
                    continue
            
            else:  # text, textarea
                answer = input(f"\nCevabınızı girin (0=atla): ").strip()
                if answer == '0':
                    return None
                elif answer:
                    return answer
                else:
                    print("Boş cevap girdiniz!")
                    continue
    
    def run_console_interface(self) -> None:
        """Konsol tabanlı arayüzü çalıştırır"""
        print("\n" + "="*80)
        print("LinkedIn Auto Job Applier - Soru Cevap Arayüzü")
        print("="*80)
        
        if not self.load_pending_questions():
            print("Sorular yüklenemedi!")
            return
        
        if not self.pending_questions:
            print("Cevaplanmamış soru bulunamadı!")
            return
        
        print(f"\n{len(self.pending_questions)} cevaplanmamış soru bulundu.")
        print("Her soru için cevap girin veya '0' yazarak atlayın.")
        print("Çıkmak için Ctrl+C tuşlayın.")
        
        answered_count = 0
        skipped_count = 0
        
        try:
            for i, question in enumerate(self.pending_questions):
                self.display_question(question, i)
                
                answer = self.get_user_answer(question)
                
                if answer is not None:
                    # Cevabı kaydet
                    success = self.question_manager.set_answer(
                        question['question_text'], 
                        question['question_type'], 
                        answer, 
                        confidence=1.0
                    )
                    
                    if success:
                        print(f"✓ Cevap kaydedildi: {answer}")
                        answered_count += 1
                    else:
                        print("✗ Cevap kaydedilemedi!")
                else:
                    print("⊘ Soru atlandı")
                    skipped_count += 1
        
        except KeyboardInterrupt:
            print("\n\nİşlem kullanıcı tarafından iptal edildi.")
        
        print(f"\n" + "="*80)
        print("ÖZET")
        print("="*80)
        print(f"Toplam soru: {len(self.pending_questions)}")
        print(f"Cevaplanan: {answered_count}")
        print(f"Atlanan: {skipped_count}")
        print(f"Kalan: {len(self.pending_questions) - answered_count - skipped_count}")
        
        # İstatistikleri güncelle
        stats = self.question_manager.get_statistics()
        print(f"\nGenel İstatistikler:")
        print(f"Toplam soru: {stats['total_questions']}")
        print(f"Cevaplanan: {stats['answered_questions']}")
        print(f"Bekleyen: {stats['pending_questions']}")
        print(f"Başarı oranı: %{stats['success_rate']*100:.1f}")
    
    def export_questions_for_web(self, file_path: str = "data/pending_questions_web.json") -> bool:
        """Web arayüzü için soruları dışa aktarır"""
        try:
            if not self.load_pending_questions():
                return False
            
            web_data = {
                "questions": self.pending_questions,
                "total_count": len(self.pending_questions),
                "export_time": self.question_manager.data["last_updated"]
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(web_data, f, indent=2, ensure_ascii=False)
            
            print_lg(f"Web arayüzü için {len(self.pending_questions)} soru dışa aktarıldı: {file_path}")
            return True
            
        except Exception as e:
            print_lg(f"Web export hatası: {e}")
            return False

def main():
    """Ana fonksiyon"""
    interface = QuestionInterface()
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "--web-export":
            interface.export_questions_for_web()
        elif sys.argv[1] == "--stats":
            stats = interface.question_manager.get_statistics()
            print(f"Toplam soru: {stats['total_questions']}")
            print(f"Cevaplanan: {stats['answered_questions']}")
            print(f"Bekleyen: {stats['pending_questions']}")
            print(f"Başarı oranı: %{stats['success_rate']*100:.1f}")
        else:
            print("Kullanım: python question_interface.py [--web-export|--stats]")
    else:
        interface.run_console_interface()

if __name__ == "__main__":
    main()
