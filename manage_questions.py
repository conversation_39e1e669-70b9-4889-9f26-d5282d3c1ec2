#!/usr/bin/env python3
"""
LinkedIn Auto Job Applier - So<PERSON> Yönetim CLI
Bu script soru-cevap sistemini yönetmek için komut satırı arayüzü <PERSON>ğ<PERSON>.
"""

import sys
import argparse
from pathlib import Path
import json

# Modül yolunu ekle
sys.path.append(str(Path(__file__).parent))

from modules.question_manager import QuestionManager
from modules.helpers import print_lg

def list_questions(qm: QuestionManager, status: str = "all"):
    """Soruları listeler"""
    if status == "pending":
        questions = qm.get_pending_questions()
        print_lg(f"Cevaplanmamış Sorular ({len(questions)} adet):")
    else:
        # Tüm soruları getir
        all_questions = []
        for category in qm.data["questions"].values():
            for question in category.values():
                all_questions.append(question)
        questions = all_questions
        print_lg(f"Tüm <PERSON>rular ({len(questions)} adet):")
    
    if not questions:
        print_lg("Soru bulunamadı!")
        return
    
    for i, q in enumerate(questions, 1):
        status_icon = "❓" if q.get("status") == "pending" else "✅"
        print_lg(f"{i:3d}. {status_icon} {q['question_text'][:80]}...")
        print_lg(f"     Tip: {q['question_type']}, Kullanım: {q.get('usage_count', 0)}, Güven: {q.get('confidence', 0):.2f}")
        
        if q.get('company_context'):
            print_lg(f"     Şirketler: {', '.join(q['company_context'][:3])}{'...' if len(q['company_context']) > 3 else ''}")
        
        if q.get('options'):
            print_lg(f"     Seçenekler: {', '.join(q['options'][:3])}{'...' if len(q['options']) > 3 else ''}")
        
        if q.get('answer'):
            answer_preview = q['answer'][:50] + "..." if len(q['answer']) > 50 else q['answer']
            print_lg(f"     Cevap: {answer_preview}")
        
        print_lg("")

def show_statistics(qm: QuestionManager):
    """İstatistikleri gösterir"""
    stats = qm.get_statistics()
    
    print_lg("=== SORU İSTATİSTİKLERİ ===")
    print_lg(f"Toplam Soru: {stats['total_questions']}")
    print_lg(f"Cevaplanan: {stats['answered_questions']}")
    print_lg(f"Bekleyen: {stats['pending_questions']}")
    print_lg(f"Başarı Oranı: %{stats['success_rate']*100:.1f}")
    print_lg("")
    
    # Kategori bazında istatistikler
    print_lg("=== KATEGORİ BAZINDA ===")
    for category_name, category in qm.data["questions"].items():
        if category:
            total = len(category)
            answered = sum(1 for q in category.values() if q.get("status") == "answered")
            print_lg(f"{category_name.replace('_', ' ').title()}: {total} toplam, {answered} cevaplı")

def export_data(qm: QuestionManager, file_path: str, format_type: str = "json"):
    """Verileri dışa aktarır"""
    try:
        if format_type == "json":
            success = qm.export_questions(file_path)
            if success:
                print_lg(f"Veriler JSON formatında dışa aktarıldı: {file_path}")
            else:
                print_lg("Dışa aktarma başarısız!")
        
        elif format_type == "csv":
            import csv
            pending_questions = qm.get_pending_questions()
            
            with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['question_text', 'question_type', 'usage_count', 'company_context', 'job_context', 'options']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for q in pending_questions:
                    writer.writerow({
                        'question_text': q['question_text'],
                        'question_type': q['question_type'],
                        'usage_count': q['usage_count'],
                        'company_context': ', '.join(q.get('company_context', [])),
                        'job_context': ', '.join(q.get('job_context', [])),
                        'options': ', '.join(q.get('options', []))
                    })
            
            print_lg(f"Veriler CSV formatında dışa aktarıldı: {file_path}")
        
        else:
            print_lg("Desteklenmeyen format! (json, csv)")
            
    except Exception as e:
        print_lg(f"Dışa aktarma hatası: {e}")

def import_answers(qm: QuestionManager, file_path: str):
    """Cevapları içe aktarır"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        imported_count = 0
        
        if 'answers' in data:
            for answer_data in data['answers']:
                success = qm.set_answer(
                    answer_data['question_text'],
                    answer_data['question_type'],
                    answer_data['answer'],
                    confidence=answer_data.get('confidence', 1.0)
                )
                if success:
                    imported_count += 1
        
        print_lg(f"{imported_count} cevap başarıyla içe aktarıldı!")
        
    except Exception as e:
        print_lg(f"İçe aktarma hatası: {e}")

def cleanup_old_questions(qm: QuestionManager, days: int):
    """Eski soruları temizler"""
    removed_count = qm.clear_old_questions(days)
    print_lg(f"{removed_count} eski soru temizlendi!")

def backup_data(qm: QuestionManager):
    """Veri yedeği oluşturur"""
    try:
        success = qm._create_backup()
        if success:
            print_lg("Veri yedeği başarıyla oluşturuldu!")
        else:
            print_lg("Yedek oluşturulamadı!")
    except Exception as e:
        print_lg(f"Yedekleme hatası: {e}")

def main():
    """Ana fonksiyon"""
    parser = argparse.ArgumentParser(description='LinkedIn Auto Job Applier - Soru Yönetim CLI')
    
    parser.add_argument('--list', choices=['all', 'pending'], default='pending',
                       help='Soruları listele (default: pending)')
    
    parser.add_argument('--stats', action='store_true',
                       help='İstatistikleri göster')
    
    parser.add_argument('--export', metavar='FILE',
                       help='Soruları dışa aktar')
    
    parser.add_argument('--format', choices=['json', 'csv'], default='json',
                       help='Dışa aktarma formatı (default: json)')
    
    parser.add_argument('--import-answers', metavar='FILE',
                       help='Cevapları içe aktar')
    
    parser.add_argument('--cleanup', type=int, metavar='DAYS',
                       help='N günden eski soruları temizle')
    
    parser.add_argument('--backup', action='store_true',
                       help='Veri yedeği oluştur')
    
    parser.add_argument('--interactive', action='store_true',
                       help='Etkileşimli mod')
    
    args = parser.parse_args()
    
    # QuestionManager örneği oluştur
    qm = QuestionManager()
    
    if args.interactive:
        interactive_mode(qm)
    elif args.stats:
        show_statistics(qm)
    elif args.export:
        export_data(qm, args.export, args.format)
    elif args.import_answers:
        import_answers(qm, args.import_answers)
    elif args.cleanup:
        cleanup_old_questions(qm, args.cleanup)
    elif args.backup:
        backup_data(qm)
    else:
        list_questions(qm, args.list)

def interactive_mode(qm: QuestionManager):
    """Etkileşimli mod"""
    print_lg("=== LinkedIn Auto Job Applier - Soru Yönetim ===")
    print_lg("Etkileşimli moda hoş geldiniz!")
    
    while True:
        print_lg("\nSeçenekler:")
        print_lg("1. Bekleyen soruları listele")
        print_lg("2. Tüm soruları listele")
        print_lg("3. İstatistikleri göster")
        print_lg("4. Veri dışa aktar")
        print_lg("5. Cevap içe aktar")
        print_lg("6. Eski soruları temizle")
        print_lg("7. Yedek oluştur")
        print_lg("0. Çıkış")
        
        try:
            choice = input("\nSeçiminizi yapın (0-7): ").strip()
            
            if choice == '0':
                print_lg("Çıkılıyor...")
                break
            elif choice == '1':
                list_questions(qm, 'pending')
            elif choice == '2':
                list_questions(qm, 'all')
            elif choice == '3':
                show_statistics(qm)
            elif choice == '4':
                file_path = input("Dosya yolu girin: ").strip()
                format_type = input("Format (json/csv): ").strip() or 'json'
                export_data(qm, file_path, format_type)
            elif choice == '5':
                file_path = input("İçe aktarılacak dosya yolu: ").strip()
                import_answers(qm, file_path)
            elif choice == '6':
                days = int(input("Kaç günden eski sorular temizlensin: ").strip())
                cleanup_old_questions(qm, days)
            elif choice == '7':
                backup_data(qm)
            else:
                print_lg("Geçersiz seçim!")
                
        except KeyboardInterrupt:
            print_lg("\nÇıkılıyor...")
            break
        except Exception as e:
            print_lg(f"Hata: {e}")

if __name__ == "__main__":
    main()
