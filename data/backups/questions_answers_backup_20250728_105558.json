{"schema_version": "1.0", "last_updated": "2025-07-28T10:55:58.069702", "questions": {"text_questions": {"80202779d9cd920033327d2a69305d21": {"id": "80202779d9cd920033327d2a69305d21", "question_text": "Ödeme Sistemleri konusunda kaç yıllık deneyiminiz bulunuyor?", "question_type": "text", "answer": "3", "confidence": 1, "created_at": "2025-07-27T22:35:10.056778", "updated_at": "2025-07-27T22:40:31.553848", "usage_count": 1, "success_count": 0, "company_context": ["ICterra Information and Communication Technologie"], "job_context": ["Ödeme Sistemleri Kıdemli Yazılım Mühendisi"], "tags": [], "status": "answered"}, "587740ffda88450b1a5c737cb329017c": {"id": "587740ffda88450b1a5c737cb329017c", "question_text": "Contractor net ücret beklentiniz nedir?", "question_type": "text", "answer": "", "confidence": 0.0, "created_at": "2025-07-28T10:55:47.501269", "updated_at": "2025-07-28T10:55:58.067263", "usage_count": 2, "success_count": 0, "company_context": ["PATH - Product and Software House"], "job_context": ["Flutter Developer (Remote)"], "tags": [], "status": "pending"}, "84115c7e1477ff483f9b949695052b24": {"id": "84115c7e1477ff483f9b949695052b24", "question_text": "cep telefonu numa<PERSON>ı", "question_type": "text", "answer": "", "confidence": 0.0, "created_at": "2025-07-28T10:55:58.069440", "updated_at": "2025-07-28T10:55:58.069442", "usage_count": 1, "success_count": 0, "company_context": ["PATH - Product and Software House"], "job_context": ["Flutter Developer (Remote)"], "tags": [], "status": "pending"}}, "textarea_questions": {}, "single_select_questions": {"ce9edecafb41b047436845031a0a4b78": {"id": "ce9edecafb41b047436845031a0a4b78", "question_text": "Contractor <PERSON><PERSON><PERSON> hizmet sözleşmesi ile istihdam sağlanacaktır. Bu çalışma şekli sizin için uygun mudur? [  \"<PERSON>ir seçenek belirleyin\", \"Yes\", \"No\", ]", "question_type": "single_select", "answer": "", "confidence": 0.0, "created_at": "2025-07-28T10:55:58.068888", "updated_at": "2025-07-28T10:55:58.068890", "usage_count": 1, "success_count": 0, "company_context": ["PATH - Product and Software House"], "job_context": ["Flutter Developer (Remote)"], "tags": [], "status": "pending", "options": [], "selected_options": []}}, "multiple_select_questions": {}, "radio_questions": {}, "checkbox_questions": {}}, "statistics": {"total_questions": 4, "answered_questions": 1, "pending_questions": 3, "success_rate": 1.0}, "settings": {"auto_answer_enabled": true, "confidence_threshold": 0.8, "backup_enabled": true, "max_backup_files": 10}}