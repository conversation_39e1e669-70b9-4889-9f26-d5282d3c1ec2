{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Question Answer Schema", "description": "LinkedIn Auto Job Applier soru-cevap sistemi için veri <PERSON>", "type": "object", "properties": {"schema_version": {"type": "string", "description": "<PERSON><PERSON> ve<PERSON>"}, "last_updated": {"type": "string", "format": "date-time", "description": "<PERSON> g<PERSON><PERSON><PERSON><PERSON> tarihi"}, "questions": {"type": "object", "properties": {"text_questions": {"$ref": "#/definitions/questionCollection"}, "textarea_questions": {"$ref": "#/definitions/questionCollection"}, "single_select_questions": {"$ref": "#/definitions/selectQuestionCollection"}, "multiple_select_questions": {"$ref": "#/definitions/selectQuestionCollection"}, "radio_questions": {"$ref": "#/definitions/selectQuestionCollection"}, "checkbox_questions": {"$ref": "#/definitions/selectQuestionCollection"}}}, "statistics": {"type": "object", "properties": {"total_questions": {"type": "integer"}, "answered_questions": {"type": "integer"}, "pending_questions": {"type": "integer"}, "success_rate": {"type": "number"}}}, "settings": {"type": "object", "properties": {"auto_answer_enabled": {"type": "boolean"}, "confidence_threshold": {"type": "number"}, "backup_enabled": {"type": "boolean"}, "max_backup_files": {"type": "integer"}}}}, "definitions": {"questionCollection": {"type": "object", "patternProperties": {"^[a-f0-9]{32}$": {"$ref": "#/definitions/question"}}}, "selectQuestionCollection": {"type": "object", "patternProperties": {"^[a-f0-9]{32}$": {"$ref": "#/definitions/selectQuestion"}}}, "question": {"type": "object", "properties": {"id": {"type": "string"}, "question_text": {"type": "string"}, "question_type": {"type": "string", "enum": ["text", "textarea", "single_select", "multiple_select", "radio", "checkbox"]}, "answer": {"type": "string"}, "confidence": {"type": "number", "minimum": 0, "maximum": 1}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "usage_count": {"type": "integer", "minimum": 0}, "success_count": {"type": "integer", "minimum": 0}, "company_context": {"type": "array", "items": {"type": "string"}}, "job_context": {"type": "array", "items": {"type": "string"}}, "tags": {"type": "array", "items": {"type": "string"}}, "status": {"type": "string", "enum": ["pending", "answered", "verified", "failed"]}}, "required": ["id", "question_text", "question_type", "created_at", "status"]}, "selectQuestion": {"allOf": [{"$ref": "#/definitions/question"}, {"properties": {"options": {"type": "array", "items": {"type": "string"}}, "selected_options": {"type": "array", "items": {"type": "string"}}}}]}}}