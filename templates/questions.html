<!DOCTYPE html>
<html lang="tr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title data-i18n="page.title">
      Soru Cevap Sistemi - LinkedIn Auto Job Applier
    </title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        background-color: #f5f5f5;
        color: #333;
        line-height: 1.6;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
      }

      .header {
        background: linear-gradient(135deg, #0077b5, #005885);
        color: white;
        padding: 30px;
        border-radius: 10px;
        margin-bottom: 30px;
        text-align: center;
      }

      .header h1 {
        font-size: 2.5em;
        margin-bottom: 10px;
      }

      .header p {
        font-size: 1.2em;
        opacity: 0.9;
      }

      .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
      }

      .stat-card {
        background: white;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        text-align: center;
      }

      .stat-number {
        font-size: 2em;
        font-weight: bold;
        color: #0077b5;
        margin-bottom: 5px;
      }

      .stat-label {
        color: #666;
        font-size: 0.9em;
      }

      .questions-container {
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        overflow: hidden;
      }

      .questions-header {
        background: #0077b5;
        color: white;
        padding: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .questions-header h2 {
        font-size: 1.5em;
      }

      .refresh-btn {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        padding: 8px 16px;
        border-radius: 5px;
        cursor: pointer;
        transition: background 0.3s;
      }

      .refresh-btn:hover {
        background: rgba(255, 255, 255, 0.3);
      }

      .question-card {
        border-bottom: 1px solid #eee;
        padding: 25px;
        transition: background 0.3s;
      }

      .question-card:hover {
        background: #f9f9f9;
      }

      .question-card:last-child {
        border-bottom: none;
      }

      .question-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 15px;
      }

      .question-text {
        font-size: 1.1em;
        font-weight: 600;
        color: #333;
        flex: 1;
        margin-right: 20px;
      }

      .question-meta {
        display: flex;
        gap: 10px;
        font-size: 0.8em;
        color: #666;
      }

      .meta-badge {
        background: #e3f2fd;
        color: #1976d2;
        padding: 2px 8px;
        border-radius: 12px;
      }

      .question-options {
        margin: 15px 0;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 5px;
      }

      .question-options h4 {
        margin-bottom: 10px;
        color: #555;
      }

      .option-list {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
      }

      .option-item {
        background: white;
        border: 1px solid #ddd;
        padding: 5px 12px;
        border-radius: 15px;
        font-size: 0.9em;
      }

      .answer-section {
        margin-top: 20px;
      }

      .answer-input {
        width: 100%;
        padding: 12px;
        border: 2px solid #ddd;
        border-radius: 5px;
        font-size: 1em;
        transition: border-color 0.3s;
      }

      .answer-input:focus {
        outline: none;
        border-color: #0077b5;
      }

      .answer-textarea {
        min-height: 100px;
        resize: vertical;
      }

      .answer-buttons {
        display: flex;
        gap: 10px;
        margin-top: 15px;
      }

      .btn {
        padding: 10px 20px;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-size: 0.9em;
        transition: all 0.3s;
      }

      .btn-primary {
        background: #0077b5;
        color: white;
      }

      .btn-primary:hover {
        background: #005885;
      }

      .btn-secondary {
        background: #6c757d;
        color: white;
      }

      .btn-secondary:hover {
        background: #545b62;
      }

      .btn-success {
        background: #28a745;
        color: white;
      }

      .loading {
        text-align: center;
        padding: 50px;
        color: #666;
      }

      .no-questions {
        text-align: center;
        padding: 50px;
        color: #666;
      }

      .no-questions h3 {
        margin-bottom: 10px;
        color: #28a745;
      }

      .context-info {
        margin: 10px 0;
        font-size: 0.9em;
        color: #666;
      }

      .context-info strong {
        color: #333;
      }

      .success-message {
        background: #d4edda;
        color: #155724;
        padding: 10px;
        border-radius: 5px;
        margin-top: 10px;
        display: none;
      }

      .error-message {
        background: #f8d7da;
        color: #721c24;
        padding: 10px;
        border-radius: 5px;
        margin-top: 10px;
        display: none;
      }

      @media (max-width: 768px) {
        .container {
          padding: 10px;
        }

        .header h1 {
          font-size: 2em;
        }

        .stats-grid {
          grid-template-columns: repeat(2, 1fr);
        }

        .question-header {
          flex-direction: column;
          align-items: flex-start;
        }

        .question-meta {
          margin-top: 10px;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>Soru Cevap Sistemi</h1>
        <p>LinkedIn Auto Job Applier için cevaplanmamış soruları yönetin</p>
      </div>

      <div class="stats-grid" id="statsGrid">
        <div class="stat-card">
          <div class="stat-number" id="totalQuestions">-</div>
          <div class="stat-label">Toplam Soru</div>
        </div>
        <div class="stat-card">
          <div class="stat-number" id="answeredQuestions">-</div>
          <div class="stat-label">Cevaplanan</div>
        </div>
        <div class="stat-card">
          <div class="stat-number" id="pendingQuestions">-</div>
          <div class="stat-label">Bekleyen</div>
        </div>
        <div class="stat-card">
          <div class="stat-number" id="successRate">-%</div>
          <div class="stat-label">Başarı Oranı</div>
        </div>
      </div>

      <div class="questions-container">
        <div class="questions-header">
          <h2>Cevaplanmamış Sorular</h2>
          <button class="refresh-btn" onclick="loadQuestions()">
            🔄 Yenile
          </button>
        </div>

        <div id="questionsContent">
          <div class="loading">
            <p>Sorular yükleniyor...</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Internationalization -->
    <script src="/static/js/i18n.js"></script>

    <script>
      let questions = [];

      // Sayfa yüklendiğinde çalışır
      document.addEventListener("DOMContentLoaded", function () {
        loadStatistics();
        loadQuestions();
      });

      // İstatistikleri yükle
      async function loadStatistics() {
        try {
          const response = await fetch("/questions/statistics");
          const stats = await response.json();

          document.getElementById("totalQuestions").textContent =
            stats.total_questions;
          document.getElementById("answeredQuestions").textContent =
            stats.answered_questions;
          document.getElementById("pendingQuestions").textContent =
            stats.pending_questions;
          document.getElementById("successRate").textContent =
            (stats.success_rate * 100).toFixed(1) + "%";
        } catch (error) {
          console.error("İstatistik yükleme hatası:", error);
        }
      }

      // Soruları yükle
      async function loadQuestions() {
        try {
          const response = await fetch("/questions/pending");
          const data = await response.json();
          questions = data.questions;

          displayQuestions();
        } catch (error) {
          console.error("Soru yükleme hatası:", error);
          document.getElementById("questionsContent").innerHTML =
            '<div class="error-message" style="display: block;">Sorular yüklenirken hata oluştu!</div>';
        }
      }

      // Soruları görüntüle
      function displayQuestions() {
        const container = document.getElementById("questionsContent");

        if (questions.length === 0) {
          container.innerHTML = `
                    <div class="no-questions">
                        <h3>🎉 Tebrikler!</h3>
                        <p>Cevaplanmamış soru bulunmuyor.</p>
                    </div>
                `;
          return;
        }

        let html = "";
        questions.forEach((question, index) => {
          html += createQuestionCard(question, index);
        });

        container.innerHTML = html;
      }

      // Soru kartı oluştur
      function createQuestionCard(question, index) {
        const optionsHtml =
          question.options && question.options.length > 0
            ? `
                <div class="question-options">
                    <h4>Seçenekler:</h4>
                    <div class="option-list">
                        ${question.options
                          .map(
                            (option) =>
                              `<span class="option-item">${option}</span>`
                          )
                          .join("")}
                    </div>
                </div>
            `
            : "";

        const contextHtml = `
                ${
                  question.company_context &&
                  question.company_context.length > 0
                    ? `<div class="context-info"><strong>Şirket:</strong> ${question.company_context.join(
                        ", "
                      )}</div>`
                    : ""
                }
                ${
                  question.job_context && question.job_context.length > 0
                    ? `<div class="context-info"><strong>İş:</strong> ${question.job_context.join(
                        ", "
                      )}</div>`
                    : ""
                }
            `;

        const inputType =
          question.question_type === "textarea" ? "textarea" : "input";
        const inputClass =
          question.question_type === "textarea"
            ? "answer-input answer-textarea"
            : "answer-input";

        return `
                <div class="question-card">
                    <div class="question-header">
                        <div class="question-text">${
                          question.question_text
                        }</div>
                        <div class="question-meta">
                            <span class="meta-badge">${
                              question.question_type
                            }</span>
                            <span class="meta-badge">Kullanım: ${
                              question.usage_count
                            }</span>
                        </div>
                    </div>
                    
                    ${contextHtml}
                    ${optionsHtml}
                    
                    <div class="answer-section">
                        ${
                          inputType === "textarea"
                            ? `<textarea class="${inputClass}" id="answer_${index}" placeholder="Cevabınızı buraya yazın..."></textarea>`
                            : `<input type="text" class="${inputClass}" id="answer_${index}" placeholder="Cevabınızı buraya yazın...">`
                        }
                        
                        <div class="answer-buttons">
                            <button class="btn btn-primary" onclick="saveAnswer(${index})">💾 Kaydet</button>
                            <button class="btn btn-secondary" onclick="skipQuestion(${index})">⏭️ Atla</button>
                        </div>
                        
                        <div class="success-message" id="success_${index}">Cevap başarıyla kaydedildi!</div>
                        <div class="error-message" id="error_${index}">Cevap kaydedilirken hata oluştu!</div>
                    </div>
                </div>
            `;
      }

      // Cevabı kaydet
      async function saveAnswer(index) {
        const question = questions[index];
        const answerInput = document.getElementById(`answer_${index}`);
        const answer = answerInput.value.trim();

        if (!answer) {
          showMessage(`error_${index}`, "Lütfen bir cevap girin!");
          return;
        }

        try {
          const response = await fetch("/questions/answer", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              question_text: question.question_text,
              question_type: question.question_type,
              answer: answer,
              confidence: 1.0,
            }),
          });

          if (response.ok) {
            showMessage(`success_${index}`, "Cevap başarıyla kaydedildi!");
            answerInput.disabled = true;

            // Soruyu listeden kaldır
            setTimeout(() => {
              questions.splice(index, 1);
              displayQuestions();
              loadStatistics();
            }, 1500);
          } else {
            showMessage(`error_${index}`, "Cevap kaydedilirken hata oluştu!");
          }
        } catch (error) {
          console.error("Kaydetme hatası:", error);
          showMessage(`error_${index}`, "Bağlantı hatası!");
        }
      }

      // Soruyu atla
      function skipQuestion(index) {
        questions.splice(index, 1);
        displayQuestions();
      }

      // Mesaj göster
      function showMessage(elementId, message) {
        const element = document.getElementById(elementId);
        element.textContent = message;
        element.style.display = "block";

        setTimeout(() => {
          element.style.display = "none";
        }, 3000);
      }
    </script>
  </body>
</html>
