<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-i18n="page.title">LinkedIn Auto Job Applier Dashboard</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .test-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 30px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .language-selector {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 10px;
        }
        
        .test-section h3 {
            color: #0077b5;
            margin-bottom: 15px;
        }
        
        .current-language {
            background: #0077b5;
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <!-- Language Selector -->
    <div class="language-selector">
        <div class="btn-group">
            <button class="btn btn-primary btn-sm" onclick="changeLanguage('tr')">
                <i class="bi bi-flag"></i> Türkçe
            </button>
            <button class="btn btn-outline-primary btn-sm" onclick="changeLanguage('en')">
                <i class="bi bi-flag"></i> English
            </button>
        </div>
        <div class="mt-2">
            <span class="current-language">
                <span data-i18n="lang.select">Dil Seçin</span>: <span id="currentLang">tr</span>
            </span>
        </div>
    </div>

    <div class="test-container">
        <h1 class="text-center mb-4">
            <i class="bi bi-translate"></i>
            <span data-i18n="page.title">LinkedIn Auto Job Applier Dashboard</span>
        </h1>
        
        <div class="alert alert-info">
            <strong>Test Sayfası:</strong> Bu sayfa çoklu dil desteğini test etmek için oluşturulmuştur.
            Yukarıdaki dil butonlarını kullanarak dil değişikliğini test edebilirsiniz.
        </div>

        <!-- Navigation Test -->
        <div class="test-section">
            <h3>Navigation Test</h3>
            <nav class="navbar navbar-expand-lg navbar-light bg-light">
                <div class="container-fluid">
                    <ul class="navbar-nav">
                        <li class="nav-item">
                            <a class="nav-link" href="#">
                                <i class="bi bi-speedometer2"></i> <span data-i18n="nav.dashboard">Dashboard</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#">
                                <i class="bi bi-question-circle"></i> <span data-i18n="nav.questions">Sorular</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#">
                                <i class="bi bi-briefcase-fill"></i> <span data-i18n="nav.nonEasyApply">Easy Apply Olmayan</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#">
                                <i class="bi bi-gear"></i> <span data-i18n="nav.settings">Ayarlar</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
        </div>

        <!-- Statistics Test -->
        <div class="test-section">
            <h3>Statistics Test</h3>
            <div class="row">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title">150</h5>
                            <p class="card-text" data-i18n="stats.totalJobs">Toplam İş</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title">120</h5>
                            <p class="card-text" data-i18n="stats.appliedJobs">Başvurulan</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title">25</h5>
                            <p class="card-text" data-i18n="stats.pendingJobs">Bekleyen</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title">80%</h5>
                            <p class="card-text" data-i18n="stats.successRate">Başarı Oranı</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filter Test -->
        <div class="test-section">
            <h3>Filter Test</h3>
            <div class="row">
                <div class="col-md-4">
                    <label class="form-label" data-i18n="filter.status">Durum</label>
                    <select class="form-select">
                        <option data-i18n="filter.status.all">Tümü</option>
                        <option data-i18n="filter.status.applied">Başvurulan</option>
                        <option data-i18n="filter.status.pending">Bekleyen</option>
                        <option data-i18n="filter.status.failed">Başarısız</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label class="form-label" data-i18n="filter.company">Şirket</label>
                    <select class="form-select">
                        <option data-i18n="filter.company.all">Tüm Şirketler</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label class="form-label" data-i18n="filter.workStyle">Çalışma Şekli</label>
                    <select class="form-select">
                        <option data-i18n="filter.workStyle.all">Tümü</option>
                        <option data-i18n="filter.workStyle.remote">Remote</option>
                        <option data-i18n="filter.workStyle.onsite">Ofiste</option>
                        <option data-i18n="filter.workStyle.hybrid">Hibrit</option>
                    </select>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-8">
                    <label class="form-label" data-i18n="filter.search">Arama</label>
                    <input type="text" class="form-control" data-i18n="filter.searchPlaceholder" placeholder="İş başlığı, şirket veya konum ara...">
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button class="btn btn-primary w-100">
                        <i class="bi bi-search"></i> <span data-i18n="filter.apply">Filtrele</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Buttons Test -->
        <div class="test-section">
            <h3>Buttons Test</h3>
            <div class="btn-group" role="group">
                <button class="btn btn-outline-primary">
                    <i class="bi bi-arrow-clockwise"></i> <span data-i18n="btn.refresh">Yenile</span>
                </button>
                <button class="btn btn-outline-success">
                    <i class="bi bi-download"></i> <span data-i18n="btn.export">Dışa Aktar</span>
                </button>
                <button class="btn btn-outline-secondary">
                    <i class="bi bi-x"></i> <span data-i18n="btn.close">Kapat</span>
                </button>
                <button class="btn btn-outline-info">
                    <i class="bi bi-gear"></i> <span data-i18n="btn.settings">Ayarlar</span>
                </button>
            </div>
        </div>

        <!-- Messages Test -->
        <div class="test-section">
            <h3>Messages Test</h3>
            <div class="alert alert-success">
                <i class="bi bi-check-circle"></i> <span data-i18n="msg.dataUpdated">Veriler güncellendi</span>
            </div>
            <div class="alert alert-warning">
                <i class="bi bi-exclamation-triangle"></i> <span data-i18n="msg.loading">Yükleniyor...</span>
            </div>
            <div class="alert alert-danger">
                <i class="bi bi-x-circle"></i> <span data-i18n="msg.error">Bir hata oluştu</span>
            </div>
            <div class="alert alert-info">
                <i class="bi bi-info-circle"></i> <span data-i18n="msg.autoRefresh">Otomatik yenileme: 30 saniye</span>
            </div>
        </div>

        <!-- Test Results -->
        <div class="test-section">
            <h3>Test Results</h3>
            <div id="testResults" class="alert alert-info">
                <strong>Test Status:</strong> <span id="testStatus">Initializing...</span><br>
                <strong>Current Language:</strong> <span id="detectedLanguage">-</span><br>
                <strong>Translations Loaded:</strong> <span id="translationsCount">-</span><br>
                <strong>Elements Translated:</strong> <span id="elementsTranslated">-</span>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Internationalization -->
    <script src="http://localhost:5001/static/js/i18n.js"></script>
    
    <script>
        function changeLanguage(lang) {
            if (window.i18n) {
                window.i18n.setLanguage(lang);
                document.getElementById('currentLang').textContent = lang;
                updateTestResults();
                
                // Update button states
                document.querySelectorAll('.language-selector .btn').forEach(btn => {
                    btn.classList.remove('btn-primary');
                    btn.classList.add('btn-outline-primary');
                });
                
                if (lang === 'tr') {
                    document.querySelector('[onclick="changeLanguage(\'tr\')"]').classList.remove('btn-outline-primary');
                    document.querySelector('[onclick="changeLanguage(\'tr\')"]').classList.add('btn-primary');
                } else {
                    document.querySelector('[onclick="changeLanguage(\'en\')"]').classList.remove('btn-outline-primary');
                    document.querySelector('[onclick="changeLanguage(\'en\')"]').classList.add('btn-primary');
                }
            }
        }
        
        function updateTestResults() {
            if (window.i18n) {
                document.getElementById('testStatus').textContent = 'Working ✓';
                document.getElementById('detectedLanguage').textContent = window.i18n.getCurrentLanguage();
                
                const translationsCount = Object.keys(translations[window.i18n.getCurrentLanguage()]).length;
                document.getElementById('translationsCount').textContent = translationsCount;
                
                const elementsWithI18n = document.querySelectorAll('[data-i18n]').length;
                document.getElementById('elementsTranslated').textContent = elementsWithI18n;
            } else {
                document.getElementById('testStatus').textContent = 'Failed ✗';
            }
        }
        
        // Initialize test results when page loads
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(updateTestResults, 1000); // Wait for i18n to initialize
        });
        
        // Listen for language changes
        window.addEventListener('languageChanged', function(event) {
            console.log('Language changed to:', event.detail.language);
            updateTestResults();
        });
    </script>
</body>
</html>
