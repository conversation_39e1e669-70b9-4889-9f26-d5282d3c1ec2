<!DOCTYPE html>
<html lang="tr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title data-i18n="page.title">LinkedIn Auto Job Applier Dashboard</title>

    <!-- Bootstrap CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <!-- Bootstrap Icons -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css"
      rel="stylesheet"
    />
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
      :root {
        --linkedin-blue: #0077b5;
        --linkedin-dark-blue: #005885;
        --success-green: #28a745;
        --warning-orange: #fd7e14;
        --danger-red: #dc3545;
      }

      body {
        background-color: #f8f9fa;
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
      }

      .navbar-brand {
        font-weight: bold;
        color: var(--linkedin-blue) !important;
      }

      .card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s, box-shadow 0.2s;
      }

      .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
      }

      .stat-card {
        background: linear-gradient(
          135deg,
          var(--linkedin-blue),
          var(--linkedin-dark-blue)
        );
        color: white;
      }

      .stat-card.success {
        background: linear-gradient(135deg, var(--success-green), #1e7e34);
      }

      .stat-card.warning {
        background: linear-gradient(135deg, var(--warning-orange), #e55a00);
      }

      .stat-card.danger {
        background: linear-gradient(135deg, var(--danger-red), #b21f2d);
      }

      .stat-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 0;
      }

      .stat-label {
        font-size: 0.9rem;
        opacity: 0.9;
      }

      .btn-linkedin {
        background-color: var(--linkedin-blue);
        border-color: var(--linkedin-blue);
        color: white;
      }

      .btn-linkedin:hover {
        background-color: var(--linkedin-dark-blue);
        border-color: var(--linkedin-dark-blue);
        color: white;
      }

      .table-hover tbody tr:hover {
        background-color: rgba(0, 119, 181, 0.05);
      }

      .badge-status {
        font-size: 0.75rem;
        padding: 0.35em 0.65em;
      }

      .job-title {
        font-weight: 600;
        color: var(--linkedin-blue);
        text-decoration: none;
      }

      .job-title:hover {
        color: var(--linkedin-dark-blue);
        text-decoration: underline;
      }

      .company-name {
        color: #6c757d;
        font-size: 0.9rem;
      }

      .filter-section {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      }

      .chart-container {
        position: relative;
        height: 300px;
      }

      .loading-spinner {
        display: none;
      }

      .loading .loading-spinner {
        display: block;
      }

      .loading .content {
        opacity: 0.5;
      }

      @media (max-width: 768px) {
        .stat-number {
          font-size: 2rem;
        }

        .table-responsive {
          font-size: 0.9rem;
        }
      }

      .sidebar {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        height: fit-content;
        position: sticky;
        top: 2rem;
      }

      .nav-pills .nav-link {
        border-radius: 10px;
        margin-bottom: 0.5rem;
        color: #6c757d;
      }

      .nav-pills .nav-link.active {
        background-color: var(--linkedin-blue);
      }

      .nav-pills .nav-link:hover:not(.active) {
        background-color: rgba(0, 119, 181, 0.1);
        color: var(--linkedin-blue);
      }
    </style>
  </head>
  <body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
      <div class="container">
        <a class="navbar-brand" href="/">
          <i class="bi bi-linkedin"></i>
          LinkedIn Auto Job Applier
        </a>

        <button
          class="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarNav"
        >
          <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarNav">
          <ul class="navbar-nav ms-auto">
            <li class="nav-item">
              <a class="nav-link active" href="#dashboard">
                <i class="bi bi-speedometer2"></i>
                <span data-i18n="nav.dashboard">Dashboard</span>
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="/questions">
                <i class="bi bi-question-circle"></i>
                <span data-i18n="nav.questions">Sorular</span>
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="/non-easy-apply">
                <i class="bi bi-briefcase-fill"></i>
                <span data-i18n="nav.nonEasyApply">Easy Apply Olmayan</span>
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#settings">
                <i class="bi bi-gear"></i>
                <span data-i18n="nav.settings">Ayarlar</span>
              </a>
            </li>
            <li class="nav-item dropdown">
              <a
                class="nav-link dropdown-toggle"
                href="#"
                id="languageDropdown"
                role="button"
                data-bs-toggle="dropdown"
              >
                <i class="bi bi-globe"></i>
                <span data-i18n="lang.select">Dil Seçin</span>
              </a>
              <ul class="dropdown-menu">
                <li>
                  <a
                    class="dropdown-item"
                    href="#"
                    onclick="i18n.setLanguage('tr')"
                  >
                    <i class="bi bi-flag"></i>
                    <span data-i18n="lang.turkish">Türkçe</span>
                  </a>
                </li>
                <li>
                  <a
                    class="dropdown-item"
                    href="#"
                    onclick="i18n.setLanguage('en')"
                  >
                    <i class="bi bi-flag"></i>
                    <span data-i18n="lang.english">English</span>
                  </a>
                </li>
              </ul>
            </li>
          </ul>
        </div>
      </div>
    </nav>

    <div class="container mt-4">
      <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 mb-4">
          <div class="sidebar">
            <h5 class="mb-3">
              <i class="bi bi-funnel"></i>
              <span data-i18n="filter.title">Filtreler</span>
            </h5>

            <ul class="nav nav-pills flex-column">
              <li class="nav-item">
                <a class="nav-link active" href="#" data-filter="all">
                  <i class="bi bi-list"></i>
                  <span data-i18n="filter.status.all">Tümü</span>
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="#" data-filter="applied">
                  <i class="bi bi-check-circle"></i>
                  <span data-i18n="filter.status.applied">Başvurulan</span>
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="#" data-filter="pending">
                  <i class="bi bi-clock"></i>
                  <span data-i18n="filter.status.pending">Bekleyen</span>
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="#" data-filter="failed">
                  <i class="bi bi-x-circle"></i>
                  <span data-i18n="filter.status.failed">Başarısız</span>
                </a>
              </li>
            </ul>

            <hr />

            <div class="mb-3">
              <label class="form-label" data-i18n="filter.dateRange"
                >Tarih Aralığı</label
              >
              <input
                type="date"
                class="form-control form-control-sm"
                id="dateFrom"
                data-i18n="filter.dateFrom"
                title="Başlangıç"
              />
              <input
                type="date"
                class="form-control form-control-sm mt-2"
                id="dateTo"
                data-i18n="filter.dateTo"
                title="Bitiş"
              />
            </div>

            <div class="mb-3">
              <label class="form-label" data-i18n="filter.company"
                >Şirket</label
              >
              <select class="form-select form-select-sm" id="companyFilter">
                <option value="" data-i18n="filter.company.all">
                  Tüm Şirketler
                </option>
              </select>
            </div>

            <div class="mb-3">
              <label class="form-label" data-i18n="filter.search">Arama</label>
              <input
                type="text"
                class="form-control form-control-sm"
                id="searchInput"
                data-i18n="filter.searchPlaceholder"
                placeholder="İş başlığı, şirket veya konum..."
              />
            </div>

            <div class="mb-3">
              <label class="form-label" data-i18n="filter.workStyle"
                >Çalışma Şekli</label
              >
              <select class="form-select form-select-sm" id="workStyleFilter">
                <option value="" data-i18n="filter.workStyle.all">Tümü</option>
                <option value="Remote" data-i18n="filter.workStyle.remote">
                  Remote
                </option>
                <option value="On-site" data-i18n="filter.workStyle.onsite">
                  Ofiste
                </option>
                <option value="Hybrid" data-i18n="filter.workStyle.hybrid">
                  Hibrit
                </option>
              </select>
            </div>

            <button
              class="btn btn-linkedin btn-sm w-100"
              onclick="applyFilters()"
            >
              <i class="bi bi-search"></i>
              <span data-i18n="filter.apply">Filtrele</span>
            </button>

            <button
              class="btn btn-outline-secondary btn-sm w-100 mt-2"
              onclick="clearFilters()"
            >
              <i class="bi bi-x-circle"></i> Temizle
            </button>
          </div>
        </div>

        <!-- Main Content -->
        <div class="col-lg-9">
          <!-- Statistics Cards -->
          <div class="row mb-4">
            <div class="col-md-3 mb-3">
              <div class="card stat-card">
                <div class="card-body text-center">
                  <div class="stat-number" id="totalJobs">-</div>
                  <div class="stat-label" data-i18n="stats.totalJobs">
                    Toplam İş
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3 mb-3">
              <div class="card stat-card success">
                <div class="card-body text-center">
                  <div class="stat-number" id="appliedJobs">-</div>
                  <div class="stat-label" data-i18n="stats.appliedJobs">
                    Başvurulan
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3 mb-3">
              <div class="card stat-card warning">
                <div class="card-body text-center">
                  <div class="stat-number" id="pendingJobs">-</div>
                  <div class="stat-label" data-i18n="stats.pendingJobs">
                    Bekleyen
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3 mb-3">
              <div class="card stat-card danger">
                <div class="card-body text-center">
                  <div class="stat-number" id="successRate">-%</div>
                  <div class="stat-label" data-i18n="stats.successRate">
                    Başarı Oranı
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Charts Row -->
          <div class="row mb-4">
            <div class="col-md-6 mb-3">
              <div class="card">
                <div class="card-header">
                  <h5 class="card-title mb-0">
                    <i class="bi bi-bar-chart"></i>
                    <span data-i18n="chart.dailyApplications"
                      >Günlük Başvuru Grafiği</span
                    >
                  </h5>
                </div>
                <div class="card-body">
                  <div class="chart-container">
                    <canvas id="dailyChart"></canvas>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-6 mb-3">
              <div class="card">
                <div class="card-header">
                  <h5 class="card-title mb-0">
                    <i class="bi bi-pie-chart"></i>
                    <span data-i18n="chart.statusDistribution"
                      >Durum Dağılımı</span
                    >
                  </h5>
                </div>
                <div class="card-body">
                  <div class="chart-container">
                    <canvas id="statusChart"></canvas>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Jobs Table -->
          <div class="card">
            <div
              class="card-header d-flex justify-content-between align-items-center"
            >
              <h5 class="card-title mb-0">
                <i class="bi bi-briefcase"></i>
                <span data-i18n="jobs.title">İş Başvuruları</span>
              </h5>
              <div>
                <button
                  class="btn btn-outline-primary btn-sm"
                  onclick="refreshData()"
                >
                  <i class="bi bi-arrow-clockwise"></i>
                  <span data-i18n="btn.refresh">Yenile</span>
                </button>
                <button
                  class="btn btn-outline-success btn-sm"
                  onclick="exportData()"
                >
                  <i class="bi bi-download"></i>
                  <span data-i18n="btn.export">Dışa Aktar</span>
                </button>
              </div>
            </div>
            <div class="card-body">
              <div class="loading-spinner text-center p-4">
                <div class="spinner-border text-primary" role="status">
                  <span class="visually-hidden" data-i18n="jobs.loading"
                    >Yükleniyor...</span
                  >
                </div>
              </div>

              <div class="content">
                <div class="table-responsive">
                  <table class="table table-hover" id="jobsTable">
                    <thead class="table-light">
                      <tr>
                        <th>#</th>
                        <th data-i18n="jobs.title">İş Başlığı</th>
                        <th data-i18n="filter.company">Şirket</th>
                        <th data-i18n="jobs.location">Konum</th>
                        <th data-i18n="jobs.appliedOn">Başvuru Tarihi</th>
                        <th data-i18n="filter.status">Durum</th>
                        <th data-i18n="jobs.viewDetails">İşlemler</th>
                      </tr>
                    </thead>
                    <tbody id="jobsTableBody">
                      <!-- Jobs will be loaded here -->
                    </tbody>
                  </table>
                </div>

                <!-- Pagination -->
                <nav aria-label="Jobs pagination">
                  <ul class="pagination justify-content-center" id="pagination">
                    <!-- Pagination will be generated here -->
                  </ul>
                </nav>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Internationalization -->
    <script src="/static/js/i18n.js"></script>

    <script>
      // Global variables
      let allJobs = [];
      let filteredJobs = [];
      let currentPage = 1;
      const jobsPerPage = 10;
      let dailyChart, statusChart;
      let autoRefreshInterval;
      let lastUpdateTime = null;

      // Initialize dashboard
      document.addEventListener("DOMContentLoaded", function () {
        loadDashboardData();
        initializeCharts();
        setupEventListeners();
        startAutoRefresh();
        setupVisibilityChange();
      });

      // Load dashboard data
      async function loadDashboardData(silent = false) {
        if (!silent) showLoading(true);

        try {
          // Load jobs data and statistics
          const [jobsResponse, statsResponse] = await Promise.all([
            fetch("/applied-jobs"),
            fetch("/job-statistics"),
          ]);

          const jobs = await jobsResponse.json();
          const stats = await statsResponse.json();

          allJobs = jobs;
          filteredJobs = jobs;

          updateStatisticsFromAPI(stats);
          updateCharts();
          displayJobs();
          populateCompanyFilter();
        } catch (error) {
          console.error("Error loading dashboard data:", error);
          showError("Veri yüklenirken hata oluştu!");
        } finally {
          if (!silent) showLoading(false);
        }
      }

      // Show/hide loading
      function showLoading(show) {
        const elements = document.querySelectorAll(".loading-spinner");
        const content = document.querySelectorAll(".content");

        elements.forEach((el) => {
          el.style.display = show ? "block" : "none";
        });

        content.forEach((el) => {
          el.style.opacity = show ? "0.5" : "1";
        });
      }

      // Update statistics from API
      function updateStatisticsFromAPI(stats) {
        document.getElementById("totalJobs").textContent = stats.total_jobs;
        document.getElementById("appliedJobs").textContent = stats.applied_jobs;
        document.getElementById("pendingJobs").textContent = stats.pending_jobs;
        document.getElementById("successRate").textContent =
          stats.success_rate.toFixed(1) + "%";
      }

      // Update statistics (for filtered data)
      function updateStatistics() {
        const total = filteredJobs.length;
        const applied = filteredJobs.filter(
          (job) => job.Date_Applied && job.Date_Applied !== "Pending"
        ).length;
        const pending = filteredJobs.filter(
          (job) => job.Date_Applied === "Pending"
        ).length;
        const successRate =
          total > 0 ? ((applied / total) * 100).toFixed(1) : 0;

        document.getElementById("totalJobs").textContent = total;
        document.getElementById("appliedJobs").textContent = applied;
        document.getElementById("pendingJobs").textContent = pending;
        document.getElementById("successRate").textContent = successRate + "%";
      }

      // Initialize charts
      function initializeCharts() {
        // Daily applications chart
        const dailyCtx = document.getElementById("dailyChart").getContext("2d");
        dailyChart = new Chart(dailyCtx, {
          type: "line",
          data: {
            labels: [],
            datasets: [
              {
                label: "Günlük Başvuru",
                data: [],
                borderColor: "rgb(0, 119, 181)",
                backgroundColor: "rgba(0, 119, 181, 0.1)",
                tension: 0.4,
              },
            ],
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                display: false,
              },
            },
          },
        });

        // Status distribution chart
        const statusCtx = document
          .getElementById("statusChart")
          .getContext("2d");
        statusChart = new Chart(statusCtx, {
          type: "doughnut",
          data: {
            labels: ["Başvurulan", "Bekleyen", "Başarısız"],
            datasets: [
              {
                data: [0, 0, 0],
                backgroundColor: [
                  "rgb(40, 167, 69)",
                  "rgb(253, 126, 20)",
                  "rgb(220, 53, 69)",
                ],
              },
            ],
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
          },
        });
      }

      // Update charts
      function updateCharts() {
        // Update daily chart
        const dailyData = getDailyApplicationData();
        dailyChart.data.labels = dailyData.labels;
        dailyChart.data.datasets[0].data = dailyData.data;
        dailyChart.update();

        // Update status chart
        const applied = filteredJobs.filter(
          (job) => job.Date_Applied && job.Date_Applied !== "Pending"
        ).length;
        const pending = filteredJobs.filter(
          (job) => job.Date_Applied === "Pending"
        ).length;
        const failed = 0; // This would need to be calculated based on your data structure

        statusChart.data.datasets[0].data = [applied, pending, failed];
        statusChart.update();
      }

      // Get daily application data
      function getDailyApplicationData() {
        const dailyCount = {};

        filteredJobs.forEach((job) => {
          if (job.Date_Applied && job.Date_Applied !== "Pending") {
            const date = new Date(job.Date_Applied).toISOString().split("T")[0];
            dailyCount[date] = (dailyCount[date] || 0) + 1;
          }
        });

        const sortedDates = Object.keys(dailyCount).sort();
        const last7Days = sortedDates.slice(-7);

        return {
          labels: last7Days,
          data: last7Days.map((date) => dailyCount[date] || 0),
        };
      }

      // Display jobs
      function displayJobs() {
        const tbody = document.getElementById("jobsTableBody");
        const startIndex = (currentPage - 1) * jobsPerPage;
        const endIndex = startIndex + jobsPerPage;
        const pageJobs = filteredJobs.slice(startIndex, endIndex);

        tbody.innerHTML = "";

        pageJobs.forEach((job, index) => {
          const row = createJobRow(job, startIndex + index + 1);
          tbody.appendChild(row);
        });

        updatePagination();
      }

      // Create job row
      function createJobRow(job, index) {
        const row = document.createElement("tr");

        const statusBadge = getStatusBadge(job.Date_Applied);

        row.innerHTML = `
                <td>${index}</td>
                <td>
                    <a href="${job.Job_Link}" target="_blank" class="job-title">
                        ${job.Title}
                    </a>
                </td>
                <td class="company-name">${job.Company}</td>
                <td><small class="text-muted">${
                  job.Work_Location || "N/A"
                }</small></td>
                <td><small>${formatDate(job.Date_Applied)}</small></td>
                <td>${statusBadge}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="viewJobDetails('${
                          job.Job_ID
                        }')" title="Detaylar">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button class="btn btn-outline-secondary" onclick="copyJobLink('${
                          job.Job_Link
                        }')" title="Linki Kopyala">
                            <i class="bi bi-link-45deg"></i>
                        </button>
                    </div>
                </td>
            `;

        return row;
      }

      // Get status badge
      function getStatusBadge(dateApplied) {
        if (!dateApplied || dateApplied === "Pending") {
          return '<span class="badge bg-warning badge-status">Bekleyen</span>';
        } else {
          return '<span class="badge bg-success badge-status">Başvurulan</span>';
        }
      }

      // Format date
      function formatDate(dateString) {
        if (!dateString || dateString === "Pending") {
          return "Bekleyen";
        }

        try {
          return new Date(dateString).toLocaleDateString("tr-TR");
        } catch {
          return dateString;
        }
      }

      // Update pagination
      function updatePagination() {
        const totalPages = Math.ceil(filteredJobs.length / jobsPerPage);
        const pagination = document.getElementById("pagination");

        pagination.innerHTML = "";

        // Previous button
        const prevLi = document.createElement("li");
        prevLi.className = `page-item ${currentPage === 1 ? "disabled" : ""}`;
        prevLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${
          currentPage - 1
        })">Önceki</a>`;
        pagination.appendChild(prevLi);

        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
          if (
            i === 1 ||
            i === totalPages ||
            (i >= currentPage - 2 && i <= currentPage + 2)
          ) {
            const li = document.createElement("li");
            li.className = `page-item ${i === currentPage ? "active" : ""}`;
            li.innerHTML = `<a class="page-link" href="#" onclick="changePage(${i})">${i}</a>`;
            pagination.appendChild(li);
          } else if (i === currentPage - 3 || i === currentPage + 3) {
            const li = document.createElement("li");
            li.className = "page-item disabled";
            li.innerHTML = '<span class="page-link">...</span>';
            pagination.appendChild(li);
          }
        }

        // Next button
        const nextLi = document.createElement("li");
        nextLi.className = `page-item ${
          currentPage === totalPages ? "disabled" : ""
        }`;
        nextLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${
          currentPage + 1
        })">Sonraki</a>`;
        pagination.appendChild(nextLi);
      }

      // Change page
      function changePage(page) {
        const totalPages = Math.ceil(filteredJobs.length / jobsPerPage);
        if (page >= 1 && page <= totalPages) {
          currentPage = page;
          displayJobs();
        }
      }

      // Populate company filter
      function populateCompanyFilter() {
        const companies = [
          ...new Set(allJobs.map((job) => job.Company)),
        ].sort();
        const select = document.getElementById("companyFilter");

        select.innerHTML = '<option value="">Tüm Şirketler</option>';
        companies.forEach((company) => {
          const option = document.createElement("option");
          option.value = company;
          option.textContent = company;
          select.appendChild(option);
        });
      }

      // Apply filters
      function applyFilters() {
        const dateFrom = document.getElementById("dateFrom").value;
        const dateTo = document.getElementById("dateTo").value;
        const company = document.getElementById("companyFilter").value;
        const searchTerm = document
          .getElementById("searchInput")
          .value.toLowerCase();
        const workStyle = document.getElementById("workStyleFilter").value;

        filteredJobs = allJobs.filter((job) => {
          let include = true;

          // Date filter
          if (dateFrom && job.Date_Applied && job.Date_Applied !== "Pending") {
            include =
              include && new Date(job.Date_Applied) >= new Date(dateFrom);
          }
          if (dateTo && job.Date_Applied && job.Date_Applied !== "Pending") {
            include = include && new Date(job.Date_Applied) <= new Date(dateTo);
          }

          // Company filter
          if (company) {
            include = include && job.Company === company;
          }

          // Search filter
          if (searchTerm) {
            const searchableText =
              `${job.Title} ${job.Company} ${job.Work_Location}`.toLowerCase();
            include = include && searchableText.includes(searchTerm);
          }

          // Work style filter
          if (workStyle) {
            include =
              include && job.Work_Style && job.Work_Style.includes(workStyle);
          }

          return include;
        });

        currentPage = 1;
        updateStatistics();
        updateCharts();
        displayJobs();
      }

      // Clear all filters
      function clearFilters() {
        document.getElementById("dateFrom").value = "";
        document.getElementById("dateTo").value = "";
        document.getElementById("companyFilter").value = "";
        document.getElementById("searchInput").value = "";
        document.getElementById("workStyleFilter").value = "";

        // Reset to all jobs
        filteredJobs = allJobs;
        currentPage = 1;
        updateStatistics();
        updateCharts();
        displayJobs();

        // Reset filter navigation
        document
          .querySelectorAll("[data-filter]")
          .forEach((l) => l.classList.remove("active"));
        document.querySelector('[data-filter="all"]').classList.add("active");
      }

      // Setup event listeners
      function setupEventListeners() {
        // Filter navigation
        document.querySelectorAll("[data-filter]").forEach((link) => {
          link.addEventListener("click", function (e) {
            e.preventDefault();

            // Update active state
            document
              .querySelectorAll("[data-filter]")
              .forEach((l) => l.classList.remove("active"));
            this.classList.add("active");

            // Apply filter
            const filter = this.dataset.filter;
            filterByStatus(filter);
          });
        });

        // Real-time search
        const searchInput = document.getElementById("searchInput");
        if (searchInput) {
          let searchTimeout;
          searchInput.addEventListener("input", function () {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
              applyFilters();
            }, 300); // 300ms delay for better performance
          });
        }

        // Auto-apply filters on change
        const filterElements = [
          "companyFilter",
          "workStyleFilter",
          "dateFrom",
          "dateTo",
        ];
        filterElements.forEach((id) => {
          const element = document.getElementById(id);
          if (element) {
            element.addEventListener("change", applyFilters);
          }
        });
      }

      // Filter by status
      function filterByStatus(status) {
        switch (status) {
          case "applied":
            filteredJobs = allJobs.filter(
              (job) => job.Date_Applied && job.Date_Applied !== "Pending"
            );
            break;
          case "pending":
            filteredJobs = allJobs.filter(
              (job) => !job.Date_Applied || job.Date_Applied === "Pending"
            );
            break;
          case "failed":
            // This would need to be implemented based on your data structure
            filteredJobs = [];
            break;
          default:
            filteredJobs = allJobs;
        }

        currentPage = 1;
        updateStatistics();
        updateCharts();
        displayJobs();
      }

      // Auto refresh functions
      function startAutoRefresh() {
        // Refresh every 30 seconds
        autoRefreshInterval = setInterval(() => {
          loadDashboardData(true); // Silent refresh
        }, 30000);

        // Update last refresh time
        updateLastRefreshTime();
      }

      function stopAutoRefresh() {
        if (autoRefreshInterval) {
          clearInterval(autoRefreshInterval);
          autoRefreshInterval = null;
        }
      }

      function updateLastRefreshTime() {
        lastUpdateTime = new Date();
        const timeString = lastUpdateTime.toLocaleTimeString("tr-TR");

        // Update refresh button text if exists
        const refreshBtn = document.querySelector('[onclick="refreshData()"]');
        if (refreshBtn) {
          refreshBtn.title = `Son güncelleme: ${timeString}`;
        }
      }

      // Setup visibility change detection
      function setupVisibilityChange() {
        document.addEventListener("visibilitychange", function () {
          if (document.hidden) {
            // Page is hidden, stop auto refresh
            stopAutoRefresh();
          } else {
            // Page is visible, restart auto refresh and refresh data
            startAutoRefresh();
            loadDashboardData(true);
          }
        });
      }

      // Utility functions
      function refreshData() {
        loadDashboardData();
        updateLastRefreshTime();
      }

      function exportData() {
        // Implement export functionality
        alert("Dışa aktarma özelliği yakında eklenecek!");
      }

      async function viewJobDetails(jobId) {
        try {
          const response = await fetch(`/job-details/${jobId}`);
          const jobDetails = await response.json();

          if (response.ok) {
            showJobDetailsModal(jobDetails);
          } else {
            showError("İş detayları yüklenemedi!");
          }
        } catch (error) {
          console.error("Error loading job details:", error);
          showError("İş detayları yüklenirken hata oluştu!");
        }
      }

      function showJobDetailsModal(job) {
        // Create modal HTML
        const modalHTML = `
          <div class="modal fade" id="jobDetailsModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
              <div class="modal-content">
                <div class="modal-header">
                  <h5 class="modal-title">${job.Title}</h5>
                  <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                  <div class="row">
                    <div class="col-md-6">
                      <h6>Şirket Bilgileri</h6>
                      <p><strong>Şirket:</strong> ${job.Company}</p>
                      <p><strong>Konum:</strong> ${job.Work_Location}</p>
                      <p><strong>Çalışma Şekli:</strong> ${job.Work_Style}</p>
                      <p><strong>HR:</strong>
                        ${
                          job.HR_Name !== "Unknown"
                            ? `<a href="${job.HR_Link}" target="_blank">${job.HR_Name}</a>`
                            : "Bilinmiyor"
                        }
                      </p>
                    </div>
                    <div class="col-md-6">
                      <h6>Başvuru Bilgileri</h6>
                      <p><strong>Başvuru Tarihi:</strong> ${formatDate(
                        job.Date_Applied
                      )}</p>
                      <p><strong>İlan Tarihi:</strong> ${formatDate(
                        job.Date_Posted
                      )}</p>
                      <p><strong>Özgeçmiş:</strong> ${job.Resume}</p>
                      <p><strong>Yeniden İlan:</strong> ${
                        job.Reposted === "True" ? "Evet" : "Hayır"
                      }</p>
                    </div>
                  </div>

                  <hr>

                  <div class="row">
                    <div class="col-12">
                      <h6>İş Açıklaması</h6>
                      <p class="text-muted">${
                        job.About_Job || "Açıklama mevcut değil"
                      }</p>
                    </div>
                  </div>

                  <div class="row">
                    <div class="col-md-6">
                      <h6>Gerekli Deneyim</h6>
                      <p>${job.Experience_Required || "Belirtilmemiş"}</p>
                    </div>
                    <div class="col-md-6">
                      <h6>Gerekli Yetenekler</h6>
                      <p>${job.Skills_Required || "Belirtilmemiş"}</p>
                    </div>
                  </div>

                  ${
                    job.Questions_Found
                      ? `
                    <div class="row">
                      <div class="col-12">
                        <h6>Sorulan Sorular</h6>
                        <p class="text-muted">${job.Questions_Found}</p>
                      </div>
                    </div>
                  `
                      : ""
                  }
                </div>
                <div class="modal-footer">
                  <a href="${
                    job.Job_Link
                  }" target="_blank" class="btn btn-linkedin">
                    <i class="bi bi-linkedin"></i> LinkedIn'de Görüntüle
                  </a>
                  ${
                    job.External_Job_link &&
                    job.External_Job_link !== "Easy Applied"
                      ? `<a href="${job.External_Job_link}" target="_blank" class="btn btn-outline-primary">
                      <i class="bi bi-box-arrow-up-right"></i> Dış Link
                    </a>`
                      : ""
                  }
                  <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Kapat</button>
                </div>
              </div>
            </div>
          </div>
        `;

        // Remove existing modal if any
        const existingModal = document.getElementById("jobDetailsModal");
        if (existingModal) {
          existingModal.remove();
        }

        // Add modal to body
        document.body.insertAdjacentHTML("beforeend", modalHTML);

        // Show modal
        const modal = new bootstrap.Modal(
          document.getElementById("jobDetailsModal")
        );
        modal.show();
      }

      function copyJobLink(link) {
        navigator.clipboard.writeText(link).then(() => {
          // Show toast notification
          showToast("Link kopyalandı!");
        });
      }

      function showError(message) {
        // Implement error notification
        console.error(message);
      }

      function showToast(message) {
        // Simple toast implementation
        const toast = document.createElement("div");
        toast.className = "toast show position-fixed top-0 end-0 m-3";
        toast.innerHTML = `
                <div class="toast-body">
                    ${message}
                </div>
            `;
        document.body.appendChild(toast);

        setTimeout(() => {
          toast.remove();
        }, 3000);
      }

      // Language change event listener
      window.addEventListener("languageChanged", function (event) {
        const newLanguage = event.detail.language;

        // Update chart labels
        updateChartLabels(newLanguage);

        // Update date/time formatting
        updateDateTimeFormatting(newLanguage);

        // Update any dynamic content
        updateDynamicContent(newLanguage);

        // Show language change notification
        const message =
          newLanguage === "tr"
            ? "Dil Türkçe olarak değiştirildi"
            : "Language changed to English";
        showToast(message);
      });

      function updateChartLabels(language) {
        // Update chart labels when language changes
        if (dailyChart) {
          const labels = language === "tr" ? ["Başvurular"] : ["Applications"];

          dailyChart.data.datasets[0].label = labels[0];
          dailyChart.update();
        }

        if (statusChart) {
          const labels =
            language === "tr"
              ? ["Başvurulan", "Bekleyen", "Başarısız"]
              : ["Applied", "Pending", "Failed"];

          statusChart.data.labels = labels;
          statusChart.update();
        }
      }

      function updateDateTimeFormatting(language) {
        // Update date/time formatting based on language
        const locale = language === "tr" ? "tr-TR" : "en-US";

        // Update last refresh time
        if (lastUpdateTime) {
          const timeString = lastUpdateTime.toLocaleTimeString(locale);
          const refreshBtn = document.querySelector(
            '[onclick="refreshData()"]'
          );
          if (refreshBtn) {
            const timeText =
              language === "tr"
                ? `Son güncelleme: ${timeString}`
                : `Last update: ${timeString}`;
            refreshBtn.title = timeText;
          }
        }

        // Re-render jobs table to update date formatting
        displayJobs();
      }

      function updateDynamicContent(language) {
        // Update any other dynamic content that needs language-specific formatting
        updateStatistics();
      }
    </script>
  </body>
</html>
