<!DOCTYPE html>
<html lang="tr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title data-i18n="page.title">
      Easy Apply <PERSON><PERSON><PERSON> - <PERSON>edIn Auto Job Applier
    </title>

    <!-- Bootstrap CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <!-- Bootstrap Icons -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css"
      rel="stylesheet"
    />

    <style>
      :root {
        --linkedin-blue: #0077b5;
        --linkedin-dark-blue: #005885;
        --success-green: #28a745;
        --warning-orange: #fd7e14;
        --danger-red: #dc3545;
      }

      body {
        background-color: #f8f9fa;
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
      }

      .navbar-brand {
        font-weight: bold;
        color: var(--linkedin-blue) !important;
      }

      .card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s, box-shadow 0.2s;
      }

      .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
      }

      .job-card {
        margin-bottom: 1.5rem;
        border-left: 4px solid var(--linkedin-blue);
      }

      .job-card.high-priority {
        border-left-color: var(--success-green);
      }

      .job-card.medium-priority {
        border-left-color: var(--warning-orange);
      }

      .job-card.low-priority {
        border-left-color: var(--danger-red);
      }

      .job-title {
        font-weight: 600;
        color: var(--linkedin-blue);
        text-decoration: none;
        font-size: 1.2rem;
      }

      .job-title:hover {
        color: var(--linkedin-dark-blue);
        text-decoration: underline;
      }

      .company-name {
        color: #6c757d;
        font-size: 1rem;
        font-weight: 500;
      }

      .score-badge {
        font-size: 0.9rem;
        padding: 0.4em 0.8em;
        border-radius: 20px;
      }

      .score-high {
        background-color: var(--success-green);
        color: white;
      }

      .score-medium {
        background-color: var(--warning-orange);
        color: white;
      }

      .score-low {
        background-color: var(--danger-red);
        color: white;
      }

      .btn-linkedin {
        background-color: var(--linkedin-blue);
        border-color: var(--linkedin-blue);
        color: white;
      }

      .btn-linkedin:hover {
        background-color: var(--linkedin-dark-blue);
        border-color: var(--linkedin-dark-blue);
        color: white;
      }

      .stats-card {
        background: linear-gradient(
          135deg,
          var(--linkedin-blue),
          var(--linkedin-dark-blue)
        );
        color: white;
      }

      .stat-number {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 0;
      }

      .stat-label {
        font-size: 0.9rem;
        opacity: 0.9;
      }

      .filter-section {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      }

      .loading-spinner {
        display: none;
      }

      .loading .loading-spinner {
        display: block;
      }

      .loading .content {
        opacity: 0.5;
      }

      .job-description {
        max-height: 100px;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .job-description.expanded {
        max-height: none;
      }

      .priority-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 8px;
      }

      .priority-high {
        background-color: var(--success-green);
      }

      .priority-medium {
        background-color: var(--warning-orange);
      }

      .priority-low {
        background-color: var(--danger-red);
      }
    </style>
  </head>
  <body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
      <div class="container">
        <a class="navbar-brand" href="/">
          <i class="bi bi-linkedin"></i>
          LinkedIn Auto Job Applier
        </a>

        <button
          class="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarNav"
        >
          <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarNav">
          <ul class="navbar-nav ms-auto">
            <li class="nav-item">
              <a class="nav-link" href="/">
                <i class="bi bi-speedometer2"></i> Dashboard
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link active" href="/non-easy-apply">
                <i class="bi bi-briefcase-fill"></i> Easy Apply Olmayan İşler
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="/questions">
                <i class="bi bi-question-circle"></i> Sorular
              </a>
            </li>
          </ul>
        </div>
      </div>
    </nav>

    <div class="container mt-4">
      <!-- Header -->
      <div class="row mb-4">
        <div class="col-12">
          <h1 class="display-6 mb-3">
            <i class="bi bi-briefcase-fill text-primary"></i>
            Easy Apply Olmayan İşler
          </h1>
          <p class="lead text-muted">
            Kriterlere uygun ancak Easy Apply özelliği olmayan işleri öncelik
            sırasına göre görüntüleyin ve manuel başvuru yapın.
          </p>
        </div>
      </div>

      <!-- Statistics Cards -->
      <div class="row mb-4">
        <div class="col-md-3 mb-3">
          <div class="card stats-card">
            <div class="card-body text-center">
              <div class="stat-number" id="totalJobs">-</div>
              <div class="stat-label">Toplam İş</div>
            </div>
          </div>
        </div>
        <div class="col-md-3 mb-3">
          <div class="card stats-card">
            <div class="card-body text-center">
              <div class="stat-number" id="pendingJobs">-</div>
              <div class="stat-label">Bekleyen</div>
            </div>
          </div>
        </div>
        <div class="col-md-3 mb-3">
          <div class="card stats-card">
            <div class="card-body text-center">
              <div class="stat-number" id="appliedJobs">-</div>
              <div class="stat-label">Başvurulan</div>
            </div>
          </div>
        </div>
        <div class="col-md-3 mb-3">
          <div class="card stats-card">
            <div class="card-body text-center">
              <div class="stat-number" id="averageScore">-</div>
              <div class="stat-label">Ortalama Skor</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Filters -->
      <div class="filter-section">
        <div class="row">
          <div class="col-md-3">
            <label class="form-label">Öncelik</label>
            <select class="form-select" id="priorityFilter">
              <option value="">Tümü</option>
              <option value="high">Yüksek</option>
              <option value="medium">Orta</option>
              <option value="low">Düşük</option>
            </select>
          </div>
          <div class="col-md-3">
            <label class="form-label">Minimum Skor</label>
            <input
              type="range"
              class="form-range"
              id="scoreFilter"
              min="0"
              max="1"
              step="0.1"
              value="0.6"
            />
            <small class="text-muted"
              >Skor: <span id="scoreValue">0.6</span></small
            >
          </div>
          <div class="col-md-3">
            <label class="form-label">Durum</label>
            <select class="form-select" id="statusFilter">
              <option value="">Tümü</option>
              <option value="pending">Bekleyen</option>
              <option value="applied">Başvurulan</option>
              <option value="ignored">Göz ardı edilen</option>
            </select>
          </div>
          <div class="col-md-3 d-flex align-items-end">
            <button class="btn btn-linkedin w-100" onclick="loadJobs()">
              <i class="bi bi-search"></i> Filtrele
            </button>
          </div>
        </div>
      </div>

      <!-- Jobs List -->
      <div class="row">
        <div class="col-12">
          <div class="loading-spinner text-center p-4">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Yükleniyor...</span>
            </div>
          </div>

          <div class="content" id="jobsList">
            <!-- Jobs will be loaded here -->
          </div>
        </div>
      </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Internationalization -->
    <script src="/static/js/i18n.js"></script>

    <script>
      let allJobs = [];

      // Initialize page
      document.addEventListener("DOMContentLoaded", function () {
        loadStatistics();
        loadJobs();
        setupEventListeners();
      });

      // Setup event listeners
      function setupEventListeners() {
        // Score filter slider
        const scoreFilter = document.getElementById("scoreFilter");
        const scoreValue = document.getElementById("scoreValue");

        scoreFilter.addEventListener("input", function () {
          scoreValue.textContent = this.value;
        });

        // Auto-apply filters on change
        ["priorityFilter", "statusFilter"].forEach((id) => {
          document.getElementById(id).addEventListener("change", loadJobs);
        });
      }

      // Load statistics
      async function loadStatistics() {
        try {
          const response = await fetch("/non-easy-apply-jobs/statistics");
          const stats = await response.json();

          document.getElementById("totalJobs").textContent = stats.total_jobs;
          document.getElementById("pendingJobs").textContent =
            stats.pending_jobs;
          document.getElementById("appliedJobs").textContent =
            stats.applied_jobs;
          document.getElementById("averageScore").textContent =
            (stats.average_score * 100).toFixed(0) + "%";
        } catch (error) {
          console.error("Error loading statistics:", error);
        }
      }

      // Load jobs
      async function loadJobs() {
        showLoading(true);

        try {
          const priority = document.getElementById("priorityFilter").value;
          const minScore = document.getElementById("scoreFilter").value;
          const status = document.getElementById("statusFilter").value;

          let url = "/non-easy-apply-jobs";
          const params = new URLSearchParams();

          if (priority) params.append("priority", priority);
          if (minScore) params.append("min_score", minScore);

          if (params.toString()) {
            url += "?" + params.toString();
          }

          const response = await fetch(url);
          const data = await response.json();

          allJobs = data.jobs;

          // Filter by status if specified
          let filteredJobs = allJobs;
          if (status) {
            filteredJobs = allJobs.filter((job) => job.status === status);
          }

          displayJobs(filteredJobs);
        } catch (error) {
          console.error("Error loading jobs:", error);
          showError("İşler yüklenirken hata oluştu!");
        } finally {
          showLoading(false);
        }
      }

      // Display jobs
      function displayJobs(jobs) {
        const jobsList = document.getElementById("jobsList");

        if (jobs.length === 0) {
          jobsList.innerHTML = `
                    <div class="text-center p-5">
                        <i class="bi bi-briefcase display-1 text-muted"></i>
                        <h3 class="mt-3">İş bulunamadı</h3>
                        <p class="text-muted">Seçilen kriterlere uygun iş bulunmuyor.</p>
                    </div>
                `;
          return;
        }

        let html = "";
        jobs.forEach((job) => {
          html += createJobCard(job);
        });

        jobsList.innerHTML = html;
      }

      // Create job card
      function createJobCard(job) {
        const score = (job.suitability_score * 100).toFixed(0);
        const scoreClass =
          score >= 80
            ? "score-high"
            : score >= 60
            ? "score-medium"
            : "score-low";
        const priorityClass = `priority-${job.priority}`;

        return `
                <div class="card job-card ${job.priority}-priority">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="d-flex align-items-center mb-2">
                                    <span class="priority-indicator ${priorityClass}"></span>
                                    <a href="${
                                      job.job_link
                                    }" target="_blank" class="job-title">
                                        ${job.title}
                                    </a>
                                </div>
                                <div class="company-name mb-2">${
                                  job.company
                                }</div>
                                <div class="text-muted mb-2">
                                    <i class="bi bi-geo-alt"></i> ${
                                      job.work_location
                                    }
                                    <span class="ms-3">
                                        <i class="bi bi-briefcase"></i> ${
                                          job.work_style
                                        }
                                    </span>
                                    <span class="ms-3">
                                        <i class="bi bi-calendar"></i> ${formatDate(
                                          job.date_posted
                                        )}
                                    </span>
                                </div>
                                <div class="job-description text-muted">
                                    ${job.description.substring(0, 200)}${
          job.description.length > 200 ? "..." : ""
        }
                                </div>
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="mb-3">
                                    <span class="score-badge ${scoreClass}">
                                        ${score}% Uygun
                                    </span>
                                </div>
                                <div class="mb-3">
                                    <small class="text-muted">
                                        Deneyim: ${job.experience_required} yıl
                                    </small>
                                </div>
                                <div class="btn-group-vertical w-100">
                                    ${
                                      job.external_link
                                        ? `<a href="${job.external_link}" target="_blank" class="btn btn-linkedin btn-sm">
                                            <i class="bi bi-box-arrow-up-right"></i> Başvur
                                        </a>`
                                        : `<a href="${job.job_link}" target="_blank" class="btn btn-linkedin btn-sm">
                                            <i class="bi bi-linkedin"></i> LinkedIn'de Aç
                                        </a>`
                                    }
                                    <button class="btn btn-outline-success btn-sm mt-1" onclick="markAsApplied('${
                                      job.job_id
                                    }')">
                                        <i class="bi bi-check"></i> Başvurdum
                                    </button>
                                    <button class="btn btn-outline-secondary btn-sm mt-1" onclick="markAsIgnored('${
                                      job.job_id
                                    }')">
                                        <i class="bi bi-x"></i> Göz Ardı Et
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
      }

      // Mark job as applied
      async function markAsApplied(jobId) {
        try {
          const response = await fetch(`/non-easy-apply-jobs/${jobId}/status`, {
            method: "PUT",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              status: "applied",
              notes: "Manuel başvuru yapıldı",
            }),
          });

          if (response.ok) {
            showToast("İş başvurulan olarak işaretlendi!", "success");
            loadJobs();
            loadStatistics();
          } else {
            showToast("İşlem başarısız!", "error");
          }
        } catch (error) {
          console.error("Error marking job as applied:", error);
          showToast("Hata oluştu!", "error");
        }
      }

      // Mark job as ignored
      async function markAsIgnored(jobId) {
        try {
          const response = await fetch(`/non-easy-apply-jobs/${jobId}/status`, {
            method: "PUT",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              status: "ignored",
              notes: "Kullanıcı tarafından göz ardı edildi",
            }),
          });

          if (response.ok) {
            showToast("İş göz ardı edildi!", "success");
            loadJobs();
            loadStatistics();
          } else {
            showToast("İşlem başarısız!", "error");
          }
        } catch (error) {
          console.error("Error marking job as ignored:", error);
          showToast("Hata oluştu!", "error");
        }
      }

      // Utility functions
      function showLoading(show) {
        const spinner = document.querySelector(".loading-spinner");
        const content = document.querySelector(".content");

        if (show) {
          spinner.style.display = "block";
          content.style.opacity = "0.5";
        } else {
          spinner.style.display = "none";
          content.style.opacity = "1";
        }
      }

      function formatDate(dateString) {
        if (!dateString) return "Bilinmiyor";
        try {
          return new Date(dateString).toLocaleDateString("tr-TR");
        } catch {
          return dateString;
        }
      }

      function showError(message) {
        console.error(message);
      }

      function showToast(message, type = "info") {
        // Simple toast implementation
        const toast = document.createElement("div");
        toast.className = `toast show position-fixed top-0 end-0 m-3 bg-${
          type === "success" ? "success" : "danger"
        } text-white`;
        toast.innerHTML = `
                <div class="toast-body">
                    ${message}
                </div>
            `;
        document.body.appendChild(toast);

        setTimeout(() => {
          toast.remove();
        }, 3000);
      }
    </script>
  </body>
</html>
