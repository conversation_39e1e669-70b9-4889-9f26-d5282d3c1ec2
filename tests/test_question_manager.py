"""
Tests for QuestionManager module
"""

import pytest
import json
import os
from pathlib import Path
from modules.question_manager import QuestionManager

class TestQuestionManager:
    """Test cases for Question<PERSON>anager"""
    
    def test_init_creates_default_structure(self, temp_dir):
        """Test that <PERSON><PERSON><PERSON><PERSON> creates default structure"""
        data_file = os.path.join(temp_dir, "test_questions.json")
        qm = QuestionManager(data_file)
        
        assert qm.data is not None
        assert "questions" in qm.data
        assert "statistics" in qm.data
        assert "settings" in qm.data
        assert qm.data["statistics"]["total_questions"] == 0
    
    def test_add_question_text_type(self, temp_dir, sample_question_data):
        """Test adding a text type question"""
        data_file = os.path.join(temp_dir, "test_questions.json")
        qm = QuestionManager(data_file)
        
        question_id = qm.add_question(
            sample_question_data["question_text"],
            sample_question_data["question_type"],
            company_context=sample_question_data["company_context"],
            job_context=sample_question_data["job_context"]
        )
        
        assert question_id != ""
        assert qm.data["statistics"]["total_questions"] == 1
        assert qm.data["statistics"]["pending_questions"] == 1
        
        # Check if question exists in correct category
        text_questions = qm.data["questions"]["text_questions"]
        assert question_id in text_questions
        
        question = text_questions[question_id]
        assert question["question_text"] == sample_question_data["question_text"]
        assert question["status"] == "pending"
        assert sample_question_data["company_context"] in question["company_context"]
    
    def test_add_question_select_type(self, temp_dir):
        """Test adding a select type question"""
        data_file = os.path.join(temp_dir, "test_questions.json")
        qm = QuestionManager(data_file)
        
        options = ["Yes", "No", "Require Sponsorship"]
        question_id = qm.add_question(
            "Are you authorized to work in the US?",
            "single_select",
            options=options
        )
        
        assert question_id != ""
        select_questions = qm.data["questions"]["single_select_questions"]
        assert question_id in select_questions
        
        question = select_questions[question_id]
        assert question["options"] == options
        assert "selected_options" in question
    
    def test_set_answer(self, temp_dir, sample_question_data):
        """Test setting an answer for a question"""
        data_file = os.path.join(temp_dir, "test_questions.json")
        qm = QuestionManager(data_file)
        
        # Add question first
        question_id = qm.add_question(
            sample_question_data["question_text"],
            sample_question_data["question_type"]
        )
        
        # Set answer
        answer = "5 years"
        success = qm.set_answer(
            sample_question_data["question_text"],
            sample_question_data["question_type"],
            answer,
            confidence=0.9
        )
        
        assert success == True
        assert qm.data["statistics"]["answered_questions"] == 1
        assert qm.data["statistics"]["pending_questions"] == 0
        
        # Check answer details
        question = qm.data["questions"]["text_questions"][question_id]
        assert question["answer"] == answer
        assert question["confidence"] == 0.9
        assert question["status"] == "answered"
    
    def test_get_answer(self, temp_dir, sample_question_data):
        """Test retrieving an answer"""
        data_file = os.path.join(temp_dir, "test_questions.json")
        qm = QuestionManager(data_file)
        
        # Add question and set answer
        qm.add_question(
            sample_question_data["question_text"],
            sample_question_data["question_type"]
        )
        
        answer = "5 years"
        qm.set_answer(
            sample_question_data["question_text"],
            sample_question_data["question_type"],
            answer,
            confidence=0.9
        )
        
        # Retrieve answer
        retrieved_answer = qm.get_answer(
            sample_question_data["question_text"],
            sample_question_data["question_type"]
        )
        
        assert retrieved_answer == answer
    
    def test_get_answer_low_confidence(self, temp_dir, sample_question_data):
        """Test that low confidence answers are not returned"""
        data_file = os.path.join(temp_dir, "test_questions.json")
        qm = QuestionManager(data_file)
        
        # Add question and set low confidence answer
        qm.add_question(
            sample_question_data["question_text"],
            sample_question_data["question_type"]
        )
        
        qm.set_answer(
            sample_question_data["question_text"],
            sample_question_data["question_type"],
            "5 years",
            confidence=0.5  # Below default threshold of 0.8
        )
        
        # Should not retrieve low confidence answer
        retrieved_answer = qm.get_answer(
            sample_question_data["question_text"],
            sample_question_data["question_type"]
        )
        
        assert retrieved_answer is None
    
    def test_get_pending_questions(self, temp_dir):
        """Test getting pending questions"""
        data_file = os.path.join(temp_dir, "test_questions.json")
        qm = QuestionManager(data_file)
        
        # Add multiple questions
        questions = [
            ("Question 1", "text"),
            ("Question 2", "textarea"),
            ("Question 3", "single_select", ["Yes", "No"])
        ]
        
        for i, q in enumerate(questions):
            if len(q) == 3:
                qm.add_question(q[0], q[1], options=q[2])
            else:
                qm.add_question(q[0], q[1])
        
        # Answer one question
        qm.set_answer("Question 1", "text", "Answer 1")
        
        # Get pending questions
        pending = qm.get_pending_questions()
        
        assert len(pending) == 2
        assert all(q["status"] == "pending" for q in pending)
        assert any(q["question_text"] == "Question 2" for q in pending)
        assert any(q["question_text"] == "Question 3" for q in pending)
    
    def test_mark_success(self, temp_dir, sample_question_data):
        """Test marking a question as successfully used"""
        data_file = os.path.join(temp_dir, "test_questions.json")
        qm = QuestionManager(data_file)
        
        # Add and answer question
        qm.add_question(
            sample_question_data["question_text"],
            sample_question_data["question_type"]
        )
        qm.set_answer(
            sample_question_data["question_text"],
            sample_question_data["question_type"],
            "5 years"
        )
        
        # Mark as successful
        success = qm.mark_success(
            sample_question_data["question_text"],
            sample_question_data["question_type"]
        )
        
        assert success == True
        
        # Check success count increased
        question_id = qm._generate_question_id(
            sample_question_data["question_text"],
            sample_question_data["question_type"]
        )
        question = qm.data["questions"]["text_questions"][question_id]
        assert question["success_count"] == 1
    
    def test_duplicate_question_handling(self, temp_dir, sample_question_data):
        """Test that duplicate questions are handled correctly"""
        data_file = os.path.join(temp_dir, "test_questions.json")
        qm = QuestionManager(data_file)
        
        # Add same question twice
        question_id1 = qm.add_question(
            sample_question_data["question_text"],
            sample_question_data["question_type"]
        )
        
        question_id2 = qm.add_question(
            sample_question_data["question_text"],
            sample_question_data["question_type"]
        )
        
        # Should return same ID and not create duplicate
        assert question_id1 == question_id2
        assert qm.data["statistics"]["total_questions"] == 1
        
        # Usage count should be incremented
        question = qm.data["questions"]["text_questions"][question_id1]
        assert question["usage_count"] == 2
    
    def test_export_questions(self, temp_dir, sample_question_data):
        """Test exporting questions to file"""
        data_file = os.path.join(temp_dir, "test_questions.json")
        export_file = os.path.join(temp_dir, "exported_questions.json")
        qm = QuestionManager(data_file)
        
        # Add some questions
        qm.add_question(
            sample_question_data["question_text"],
            sample_question_data["question_type"]
        )
        
        # Export
        success = qm.export_questions(export_file)
        assert success == True
        assert os.path.exists(export_file)
        
        # Check export content
        with open(export_file, 'r', encoding='utf-8') as f:
            exported_data = json.load(f)
        
        assert "questions" in exported_data
        assert "statistics" in exported_data
        assert len(exported_data["questions"]) == 1
    
    def test_update_statistics(self, temp_dir):
        """Test statistics update functionality"""
        data_file = os.path.join(temp_dir, "test_questions.json")
        qm = QuestionManager(data_file)
        
        # Add questions with different statuses
        qm.add_question("Question 1", "text")
        qm.add_question("Question 2", "text")
        qm.add_question("Question 3", "text")
        
        # Answer some questions
        qm.set_answer("Question 1", "text", "Answer 1")
        qm.set_answer("Question 2", "text", "Answer 2")
        
        # Update statistics
        qm.update_statistics()
        
        stats = qm.data["statistics"]
        assert stats["total_questions"] == 3
        assert stats["answered_questions"] == 2
        assert stats["pending_questions"] == 1
        assert stats["success_rate"] == pytest.approx(2/3, rel=1e-2)
    
    def test_clear_old_questions(self, temp_dir):
        """Test clearing old questions functionality"""
        data_file = os.path.join(temp_dir, "test_questions.json")
        qm = QuestionManager(data_file)
        
        # Add a question
        qm.add_question("Old Question", "text")
        
        # Manually set old creation date
        question_id = qm._generate_question_id("Old Question", "text")
        question = qm.data["questions"]["text_questions"][question_id]
        question["created_at"] = "2024-01-01T00:00:00"
        question["usage_count"] = 1
        
        # Clear old questions (30 days)
        removed_count = qm.clear_old_questions(30)
        
        assert removed_count == 1
        assert question_id not in qm.data["questions"]["text_questions"]
