"""
Test configuration and fixtures for LinkedIn Auto Job Applier
"""

import pytest
import tempfile
import os
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

@pytest.fixture
def temp_dir():
    """Create a temporary directory for tests"""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield temp_dir

@pytest.fixture
def sample_job_data():
    """Sample job data for testing"""
    return {
        "job_id": "test_job_123",
        "title": "Software Engineer",
        "company": "TechCorp",
        "work_location": "Remote",
        "work_style": "Remote",
        "description": "We are looking for a Python developer with React experience",
        "experience_required": 3,
        "skills": "Python, React, JavaScript",
        "hr_name": "John Doe",
        "hr_link": "https://linkedin.com/in/johndoe",
        "date_posted": "2025-01-27",
        "job_link": "https://linkedin.com/jobs/view/test_job_123",
        "external_link": "https://company.com/apply/123"
    }

@pytest.fixture
def sample_question_data():
    """Sample question data for testing"""
    return {
        "question_text": "How many years of experience do you have with Python?",
        "question_type": "text",
        "company_context": "TechCorp",
        "job_context": "Software Engineer"
    }

@pytest.fixture
def mock_flask_app():
    """Mock Flask app for testing"""
    from app import app
    app.config['TESTING'] = True
    with app.test_client() as client:
        yield client
