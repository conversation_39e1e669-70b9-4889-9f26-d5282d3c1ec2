"""
Tests for Flask API endpoints
"""

import pytest
import json
import tempfile
import os
from unittest.mock import patch, MagicMock

class TestFlaskAPI:
    """Test cases for Flask API endpoints"""
    
    def test_home_route(self, mock_flask_app):
        """Test home route returns dashboard"""
        response = mock_flask_app.get('/')
        assert response.status_code == 200
        assert b'LinkedIn Auto Job Applier Dashboard' in response.data
    
    def test_questions_route(self, mock_flask_app):
        """Test questions route"""
        response = mock_flask_app.get('/questions')
        assert response.status_code == 200
        assert b'Soru Cevap Sistemi' in response.data
    
    def test_dashboard_route(self, mock_flask_app):
        """Test dashboard route"""
        response = mock_flask_app.get('/dashboard')
        assert response.status_code == 200
        assert b'LinkedIn Auto Job Applier Dashboard' in response.data
    
    def test_non_easy_apply_route(self, mock_flask_app):
        """Test non-easy-apply route"""
        response = mock_flask_app.get('/non-easy-apply')
        assert response.status_code == 200
        assert b'Easy Apply <PERSON>' in response.data
    
    @patch('csv.DictReader')
    @patch('builtins.open')
    def test_applied_jobs_api(self, mock_open, mock_csv_reader, mock_flask_app):
        """Test applied jobs API endpoint"""
        # Mock CSV data
        mock_csv_reader.return_value = [
            {
                'Job ID': 'test_123',
                'Title': 'Software Engineer',
                'Company': 'TechCorp',
                'Work Location': 'Remote',
                'Date Applied': '2025-01-27'
            }
        ]
        
        response = mock_flask_app.get('/applied-jobs')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert isinstance(data, list)
        assert len(data) == 1
        assert data[0]['Job_ID'] == 'test_123'
        assert data[0]['Title'] == 'Software Engineer'
    
    @patch('csv.DictReader')
    @patch('builtins.open')
    def test_applied_jobs_api_file_not_found(self, mock_open, mock_csv_reader, mock_flask_app):
        """Test applied jobs API when file not found"""
        mock_open.side_effect = FileNotFoundError()
        
        response = mock_flask_app.get('/applied-jobs')
        assert response.status_code == 404
        
        data = json.loads(response.data)
        assert 'error' in data
        assert 'No applications history found' in data['error']
    
    def test_questions_pending_api(self, mock_flask_app):
        """Test pending questions API endpoint"""
        with patch('app.question_manager') as mock_qm:
            mock_qm.get_pending_questions.return_value = [
                {
                    'id': 'test_q1',
                    'question_text': 'Test question?',
                    'question_type': 'text',
                    'usage_count': 1
                }
            ]
            
            response = mock_flask_app.get('/questions/pending')
            assert response.status_code == 200
            
            data = json.loads(response.data)
            assert 'questions' in data
            assert 'total_count' in data
            assert len(data['questions']) == 1
            assert data['questions'][0]['question_text'] == 'Test question?'
    
    def test_questions_answer_api(self, mock_flask_app):
        """Test answer question API endpoint"""
        with patch('app.question_manager') as mock_qm:
            mock_qm.set_answer.return_value = True
            
            response = mock_flask_app.post('/questions/answer',
                json={
                    'question_text': 'Test question?',
                    'question_type': 'text',
                    'answer': 'Test answer',
                    'confidence': 1.0
                })
            
            assert response.status_code == 200
            
            data = json.loads(response.data)
            assert data['message'] == 'Answer saved successfully'
            
            # Verify the method was called with correct parameters
            mock_qm.set_answer.assert_called_once_with(
                'Test question?', 'text', 'Test answer', 1.0
            )
    
    def test_questions_answer_api_missing_fields(self, mock_flask_app):
        """Test answer question API with missing fields"""
        response = mock_flask_app.post('/questions/answer',
            json={
                'question_text': 'Test question?',
                # Missing question_type and answer
            })
        
        assert response.status_code == 400
        
        data = json.loads(response.data)
        assert 'error' in data
        assert 'Missing required fields' in data['error']
    
    def test_questions_statistics_api(self, mock_flask_app):
        """Test question statistics API endpoint"""
        with patch('app.question_manager') as mock_qm:
            mock_qm.get_statistics.return_value = {
                'total_questions': 10,
                'answered_questions': 7,
                'pending_questions': 3,
                'success_rate': 0.7
            }
            
            response = mock_flask_app.get('/questions/statistics')
            assert response.status_code == 200
            
            data = json.loads(response.data)
            assert data['total_questions'] == 10
            assert data['answered_questions'] == 7
            assert data['pending_questions'] == 3
            assert data['success_rate'] == 0.7
    
    def test_non_easy_apply_jobs_api(self, mock_flask_app):
        """Test non-easy-apply jobs API endpoint"""
        with patch('app.non_easy_apply_manager') as mock_neam:
            mock_neam.get_pending_jobs.return_value = [
                {
                    'job_id': 'test_job_1',
                    'title': 'Software Engineer',
                    'company': 'TechCorp',
                    'suitability_score': 0.85,
                    'priority': 'high'
                }
            ]
            
            response = mock_flask_app.get('/non-easy-apply-jobs')
            assert response.status_code == 200
            
            data = json.loads(response.data)
            assert 'jobs' in data
            assert 'total_count' in data
            assert len(data['jobs']) == 1
            assert data['jobs'][0]['title'] == 'Software Engineer'
    
    def test_non_easy_apply_jobs_with_priority_filter(self, mock_flask_app):
        """Test non-easy-apply jobs API with priority filter"""
        with patch('app.non_easy_apply_manager') as mock_neam:
            mock_neam.get_jobs_by_priority.return_value = [
                {
                    'job_id': 'test_job_1',
                    'title': 'Senior Engineer',
                    'priority': 'high'
                }
            ]
            
            response = mock_flask_app.get('/non-easy-apply-jobs?priority=high&limit=10')
            assert response.status_code == 200
            
            # Verify the method was called with correct parameters
            mock_neam.get_jobs_by_priority.assert_called_once_with('high', 10)
    
    def test_non_easy_apply_statistics_api(self, mock_flask_app):
        """Test non-easy-apply statistics API endpoint"""
        with patch('app.non_easy_apply_manager') as mock_neam:
            mock_neam.get_statistics.return_value = {
                'total_jobs': 25,
                'pending_jobs': 15,
                'applied_jobs': 8,
                'ignored_jobs': 2,
                'average_score': 0.72
            }
            
            response = mock_flask_app.get('/non-easy-apply-jobs/statistics')
            assert response.status_code == 200
            
            data = json.loads(response.data)
            assert data['total_jobs'] == 25
            assert data['pending_jobs'] == 15
            assert data['applied_jobs'] == 8
            assert data['average_score'] == 0.72
    
    def test_update_non_easy_apply_job_status(self, mock_flask_app):
        """Test updating non-easy-apply job status"""
        with patch('app.non_easy_apply_manager') as mock_neam:
            mock_neam.update_job_status.return_value = True
            
            response = mock_flask_app.put('/non-easy-apply-jobs/test_job_1/status',
                json={
                    'status': 'applied',
                    'notes': 'Applied manually'
                })
            
            assert response.status_code == 200
            
            data = json.loads(response.data)
            assert data['message'] == 'Job status updated successfully'
            
            # Verify the method was called with correct parameters
            mock_neam.update_job_status.assert_called_once_with(
                'test_job_1', 'applied', 'Applied manually'
            )
    
    def test_update_non_easy_apply_job_status_not_found(self, mock_flask_app):
        """Test updating non-easy-apply job status when job not found"""
        with patch('app.non_easy_apply_manager') as mock_neam:
            mock_neam.update_job_status.return_value = False
            
            response = mock_flask_app.put('/non-easy-apply-jobs/nonexistent/status',
                json={
                    'status': 'applied'
                })
            
            assert response.status_code == 404
            
            data = json.loads(response.data)
            assert 'error' in data
            assert 'Job not found' in data['error']
    
    def test_update_non_easy_apply_job_status_missing_status(self, mock_flask_app):
        """Test updating non-easy-apply job status without status field"""
        response = mock_flask_app.put('/non-easy-apply-jobs/test_job_1/status',
            json={
                'notes': 'Some notes'
                # Missing status field
            })
        
        assert response.status_code == 400
        
        data = json.loads(response.data)
        assert 'error' in data
        assert 'Status is required' in data['error']
    
    @patch('csv.DictReader')
    @patch('builtins.open')
    def test_job_details_api(self, mock_open, mock_csv_reader, mock_flask_app):
        """Test job details API endpoint"""
        mock_csv_reader.return_value = [
            {
                'Job ID': 'test_123',
                'Title': 'Software Engineer',
                'Company': 'TechCorp',
                'Work Location': 'Remote',
                'About Job': 'Great opportunity...',
                'Date Applied': '2025-01-27'
            }
        ]
        
        response = mock_flask_app.get('/job-details/test_123')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['Job_ID'] == 'test_123'
        assert data['Title'] == 'Software Engineer'
        assert data['About_Job'] == 'Great opportunity...'
    
    @patch('csv.DictReader')
    @patch('builtins.open')
    def test_job_details_api_not_found(self, mock_open, mock_csv_reader, mock_flask_app):
        """Test job details API when job not found"""
        mock_csv_reader.return_value = [
            {
                'Job ID': 'different_job',
                'Title': 'Other Job'
            }
        ]
        
        response = mock_flask_app.get('/job-details/nonexistent_job')
        assert response.status_code == 404
        
        data = json.loads(response.data)
        assert 'error' in data
        assert 'Job not found' in data['error']
