"""
Integration tests for LinkedIn Auto Job Applier
"""

import pytest
import tempfile
import os
import json
from unittest.mock import patch, MagicMock
from modules.question_manager import QuestionManager
from modules.non_easy_apply_manager import NonEasyApplyManager

class TestIntegration:
    """Integration test cases"""
    
    def test_question_manager_integration(self, temp_dir):
        """Test complete question management workflow"""
        data_file = os.path.join(temp_dir, "integration_questions.json")
        qm = QuestionManager(data_file)
        
        # Scenario: User encounters questions during job application
        questions = [
            ("How many years of experience do you have with Python?", "text"),
            ("Are you authorized to work in the US?", "single_select", ["Yes", "No", "Require Sponsorship"]),
            ("Please describe your experience with machine learning", "textarea"),
            ("Do you have experience with the following technologies?", "multiple_select", ["React", "Node.js", "Python", "Docker"])
        ]
        
        # Step 1: Add questions as they are encountered
        question_ids = []
        for q in questions:
            if len(q) == 3:
                qid = qm.add_question(q[0], q[1], options=q[2], company_context="TechCorp", job_context="Software Engineer")
            else:
                qid = qm.add_question(q[0], q[1], company_context="TechCorp", job_context="Software Engineer")
            question_ids.append(qid)
        
        # Verify all questions were added
        assert qm.data["statistics"]["total_questions"] == 4
        assert qm.data["statistics"]["pending_questions"] == 4
        
        # Step 2: User provides answers through interface
        answers = [
            ("How many years of experience do you have with Python?", "text", "5 years", 1.0),
            ("Are you authorized to work in the US?", "single_select", "Yes", 1.0),
            ("Please describe your experience with machine learning", "textarea", "I have 3 years of experience with ML, including deep learning and NLP projects.", 0.9),
            ("Do you have experience with the following technologies?", "multiple_select", "React, Python, Docker", 0.8)
        ]
        
        for question_text, question_type, answer, confidence in answers:
            success = qm.set_answer(question_text, question_type, answer, confidence)
            assert success == True
        
        # Verify statistics updated
        assert qm.data["statistics"]["answered_questions"] == 4
        assert qm.data["statistics"]["pending_questions"] == 0
        assert qm.data["statistics"]["success_rate"] == 1.0
        
        # Step 3: Next job application - questions should be auto-answered
        for question_text, question_type, expected_answer, _ in answers:
            retrieved_answer = qm.get_answer(question_text, question_type)
            assert retrieved_answer == expected_answer
            
            # Mark as successfully used
            qm.mark_success(question_text, question_type)
        
        # Step 4: Export for backup
        export_file = os.path.join(temp_dir, "exported_questions.json")
        success = qm.export_questions(export_file)
        assert success == True
        assert os.path.exists(export_file)
        
        # Verify export content
        with open(export_file, 'r', encoding='utf-8') as f:
            exported_data = json.load(f)
        assert len(exported_data["questions"]) == 0  # All answered, so no pending questions
    
    def test_non_easy_apply_manager_integration(self, temp_dir, sample_job_data):
        """Test complete non-easy-apply job management workflow"""
        data_file = os.path.join(temp_dir, "integration_jobs.json")
        neam = NonEasyApplyManager(data_file)
        
        # Scenario: Bot finds jobs without Easy Apply
        jobs = [
            {
                **sample_job_data,
                "job_id": "job_1",
                "title": "Senior Python Developer",
                "company": "BigTech Corp",
                "description": "We need a senior Python developer with 5+ years experience",
                "experience_required": 5
            },
            {
                **sample_job_data,
                "job_id": "job_2", 
                "title": "Junior React Developer",
                "company": "Startup Inc",
                "description": "Looking for a junior React developer, fresh graduates welcome",
                "experience_required": 1
            },
            {
                **sample_job_data,
                "job_id": "job_3",
                "title": "Machine Learning Engineer",
                "company": "AI Company",
                "description": "ML engineer position requiring Python, TensorFlow, and 3+ years experience",
                "experience_required": 3
            }
        ]
        
        # Step 1: Add jobs as they are discovered
        for job in jobs:
            success = neam.add_job(**job)
            assert success == True
        
        # Verify jobs were added and scored
        assert neam.data["statistics"]["total_jobs"] == 3
        assert neam.data["statistics"]["pending_jobs"] == 3
        
        # Step 2: Get jobs by priority for user review
        high_priority_jobs = neam.get_jobs_by_priority("high")
        medium_priority_jobs = neam.get_jobs_by_priority("medium")
        low_priority_jobs = neam.get_jobs_by_priority("low")
        
        total_prioritized = len(high_priority_jobs) + len(medium_priority_jobs) + len(low_priority_jobs)
        assert total_prioritized == 3
        
        # Step 3: User applies to some jobs manually
        pending_jobs = neam.get_pending_jobs(min_score=0.5)
        assert len(pending_jobs) >= 1
        
        # Apply to first job
        first_job = pending_jobs[0]
        success = neam.update_job_status(first_job["job_id"], "applied", "Applied manually through company website")
        assert success == True
        
        # Ignore second job
        if len(pending_jobs) > 1:
            second_job = pending_jobs[1]
            success = neam.update_job_status(second_job["job_id"], "ignored", "Not interested in this role")
            assert success == True
        
        # Step 4: Check updated statistics
        stats = neam.get_statistics()
        assert stats["applied_jobs"] >= 1
        assert stats["total_jobs"] == 3
        
        # Step 5: Export to CSV
        csv_success = neam._export_to_csv()
        assert csv_success == True
        assert neam.csv_file.exists()
    
    def test_full_system_integration(self, temp_dir):
        """Test integration between question manager and non-easy-apply manager"""
        # Setup both managers
        question_data_file = os.path.join(temp_dir, "system_questions.json")
        job_data_file = os.path.join(temp_dir, "system_jobs.json")
        
        qm = QuestionManager(question_data_file)
        neam = NonEasyApplyManager(job_data_file)
        
        # Scenario: Complete job application workflow
        
        # Step 1: Bot finds a job without Easy Apply
        job_success = neam.add_job(
            job_id="system_test_job",
            title="Full Stack Developer",
            company="Integration Corp",
            work_location="Remote",
            work_style="Remote",
            description="Full stack developer position requiring Python and React",
            experience_required=3,
            skills="Python, React, JavaScript",
            hr_name="Jane Smith",
            hr_link="https://linkedin.com/in/janesmith",
            date_posted="2025-01-27",
            job_link="https://linkedin.com/jobs/view/system_test_job",
            external_link="https://company.com/apply/system_test_job"
        )
        assert job_success == True
        
        # Step 2: During manual application, user encounters questions
        questions = [
            ("What is your expected salary range?", "text"),
            ("Are you willing to relocate?", "single_select", ["Yes", "No", "Maybe"])
        ]
        
        for q in questions:
            if len(q) == 3:
                qm.add_question(q[0], q[1], options=q[2], company_context="Integration Corp", job_context="Full Stack Developer")
            else:
                qm.add_question(q[0], q[1], company_context="Integration Corp", job_context="Full Stack Developer")
        
        # Step 3: User provides answers
        qm.set_answer("What is your expected salary range?", "text", "$80,000 - $100,000", 1.0)
        qm.set_answer("Are you willing to relocate?", "single_select", "No", 1.0)
        
        # Step 4: User completes application
        neam.update_job_status("system_test_job", "applied", "Applied with salary expectation $80k-$100k")
        
        # Step 5: Verify system state
        # Question manager should have answered questions
        assert qm.data["statistics"]["answered_questions"] == 2
        
        # Non-easy-apply manager should have applied job
        neam_stats = neam.get_statistics()
        assert neam_stats["applied_jobs"] == 1
        
        # Step 6: Next similar job should use saved answers
        salary_answer = qm.get_answer("What is your expected salary range?", "text")
        relocate_answer = qm.get_answer("Are you willing to relocate?", "single_select")
        
        assert salary_answer == "$80,000 - $100,000"
        assert relocate_answer == "No"
    
    @patch('modules.question_manager.QuestionManager._save_data')
    def test_error_handling_integration(self, mock_save, temp_dir):
        """Test system behavior under error conditions"""
        data_file = os.path.join(temp_dir, "error_test_questions.json")
        qm = QuestionManager(data_file)
        
        # Simulate save failure
        mock_save.return_value = False
        
        # System should handle save failures gracefully
        question_id = qm.add_question("Test question?", "text")
        # Should still return an ID even if save fails
        assert question_id != ""
        
        # But internal state should still be updated
        assert qm.data["statistics"]["total_questions"] == 1
    
    def test_concurrent_access_simulation(self, temp_dir):
        """Test behavior when multiple instances access same data"""
        data_file = os.path.join(temp_dir, "concurrent_questions.json")
        
        # Create two manager instances (simulating concurrent access)
        qm1 = QuestionManager(data_file)
        qm2 = QuestionManager(data_file)
        
        # Both add questions
        qm1.add_question("Question from instance 1", "text")
        qm2.add_question("Question from instance 2", "text")
        
        # Reload to get latest state
        qm1_reloaded = QuestionManager(data_file)
        
        # Should have both questions (last write wins)
        # This test demonstrates the limitation of concurrent access
        # In a real system, you'd want proper locking or database
        assert qm1_reloaded.data["statistics"]["total_questions"] >= 1
    
    def test_data_migration_compatibility(self, temp_dir):
        """Test that system handles data format changes gracefully"""
        data_file = os.path.join(temp_dir, "migration_test.json")
        
        # Create old format data
        old_format_data = {
            "version": "0.9",  # Old version
            "questions": {
                "text_questions": {},
                "textarea_questions": {}
                # Missing some new categories
            },
            "statistics": {
                "total_questions": 0
                # Missing some new stats
            }
            # Missing settings section
        }
        
        with open(data_file, 'w', encoding='utf-8') as f:
            json.dump(old_format_data, f)
        
        # System should handle old format gracefully
        qm = QuestionManager(data_file)
        
        # Should have all required sections
        assert "settings" in qm.data
        assert "single_select_questions" in qm.data["questions"]
        assert "success_rate" in qm.data["statistics"]
        
        # Should be able to add questions normally
        question_id = qm.add_question("Migration test question", "text")
        assert question_id != ""
