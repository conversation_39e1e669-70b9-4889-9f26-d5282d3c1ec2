#!/usr/bin/env python3
"""
LinkedIn Auto Job Applier - Unified Starter
Tek komutla hem bot'u hem dashboard'u başlatır
"""

import subprocess
import sys
import os
import time
import signal
import threading
import argparse
from pathlib import Path

class LinkedInAutoJobApplierStarter:
    def __init__(self, interactive=True, with_logs=False):
        self.project_root = Path(__file__).parent
        self.processes = []
        self.running = True
        self.interactive = interactive
        self.with_logs = with_logs
        self.log_file_path = self.project_root / "logs" / "log.txt"
        
    def print_banner(self):
        """Başlangıç banner'ını yazdır"""
        banner = """
╔══════════════════════════════════════════════════════════════╗
║                LinkedIn Auto Job Applier                     ║
║                    Unified Starter                           ║
╠══════════════════════════════════════════════════════════════╣
║  🤖 Bot: Automated job applications                         ║
║  📊 Dashboard: Web interface (http://localhost:5001)        ║
║  ❓ Questions: Question management                           ║
║  🎯 Non-Easy Apply: Manual application management           ║
╚══════════════════════════════════════════════════════════════╝
        """
        print(banner)
        
    def check_requirements(self):
        """Gerekli dosyaların varlığını kontrol et"""
        required_files = [
            'app.py',
            'runAiBot.py',
            'requirements.txt',
            'venv/bin/activate'  # Unix/Mac için
        ]
        
        missing_files = []
        for file_path in required_files:
            if not (self.project_root / file_path).exists():
                # Windows için alternatif kontrol
                if file_path == 'venv/bin/activate':
                    if not (self.project_root / 'venv/Scripts/activate').exists():
                        missing_files.append(file_path)
                else:
                    missing_files.append(file_path)
        
        if missing_files:
            print(f"❌ Missing files: {', '.join(missing_files)}")
            return False

        print("✅ All required files are present")
        return True
    
    def get_python_command(self):
        """Doğru Python komutunu belirle"""
        # Virtual environment'ı kontrol et
        venv_python_unix = self.project_root / 'venv/bin/python'
        venv_python_win = self.project_root / 'venv/Scripts/python.exe'
        
        if venv_python_unix.exists():
            return str(venv_python_unix)
        elif venv_python_win.exists():
            return str(venv_python_win)
        else:
            # System Python'u kullan
            return sys.executable
    
    def start_dashboard(self):
        """Start Dashboard (Flask app)"""
        print("🚀 Starting Dashboard...")

        python_cmd = self.get_python_command()
        app_path = self.project_root / 'app.py'

        try:
            process = subprocess.Popen(
                [python_cmd, str(app_path)],
                cwd=str(self.project_root),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )

            self.processes.append(('Dashboard', process))
            print("✅ Dashboard started (PID: {})".format(process.pid))
            print("📊 Web interface: http://localhost:5001")

            return process

        except Exception as e:
            print(f"❌ Failed to start Dashboard: {e}")
            return None
    
    def start_bot(self):
        """Start Bot (runAiBot.py)"""
        print("🤖 Starting Bot...")

        python_cmd = self.get_python_command()
        bot_path = self.project_root / 'runAiBot.py'

        try:
            process = subprocess.Popen(
                [python_cmd, str(bot_path)],
                cwd=str(self.project_root),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )

            self.processes.append(('Bot', process))
            print("✅ Bot started (PID: {})".format(process.pid))

            return process

        except Exception as e:
            print(f"❌ Failed to start Bot: {e}")
            return None
    
    def monitor_processes(self):
        """Process'leri izle ve çıktılarını göster"""
        def monitor_process(name, process):
            try:
                while self.running and process.poll() is None:
                    # stdout'u oku
                    if process.stdout:
                        line = process.stdout.readline()
                        if line:
                            print(f"[{name}] {line.strip()}")

                    # stderr'u oku
                    if process.stderr:
                        line = process.stderr.readline()
                        if line:
                            print(f"[{name}] ERROR: {line.strip()}")

                    time.sleep(0.1)

            except Exception as e:
                print(f"[{name}] Monitoring error: {e}")

        # Her process için ayrı thread başlat
        threads = []
        for name, process in self.processes:
            thread = threading.Thread(target=monitor_process, args=(name, process))
            thread.daemon = True
            thread.start()
            threads.append(thread)

        return threads

    def follow_log_file(self):
        """Log dosyasını takip et ve terminalde göster"""
        def tail_log():
            try:
                # Log dosyasının var olmasını bekle
                while not self.log_file_path.exists() and self.running:
                    time.sleep(1)

                if not self.running:
                    return

                print(f"📋 Following log file: {self.log_file_path}")
                print("=" * 60)

                with open(self.log_file_path, 'r', encoding='utf-8') as file:
                    # Dosyanın sonuna git
                    file.seek(0, 2)

                    while self.running:
                        line = file.readline()
                        if line:
                            # Log satırını temizle ve yazdır
                            clean_line = line.strip()
                            if clean_line:
                                print(f"🤖 {clean_line}")
                        else:
                            time.sleep(0.1)

            except Exception as e:
                print(f"❌ Log takip hatası: {e}")

        if self.with_logs:
            log_thread = threading.Thread(target=tail_log)
            log_thread.daemon = True
            log_thread.start()
            return log_thread
        return None
    
    def signal_handler(self, signum, frame):
        """Ctrl+C ile temiz kapatma"""
        print("\n🛑 Kapatma sinyali alındı...")
        self.stop_all()
        sys.exit(0)
    
    def stop_all(self):
        """Tüm process'leri durdur"""
        self.running = False
        
        print("🛑 Tüm process'ler durduruluyor...")
        
        for name, process in self.processes:
            try:
                if process.poll() is None:  # Hala çalışıyorsa
                    print(f"⏹️  {name} durduruluyor...")
                    process.terminate()
                    
                    # 5 saniye bekle
                    try:
                        process.wait(timeout=5)
                        print(f"✅ {name} başarıyla durduruldu")
                    except subprocess.TimeoutExpired:
                        print(f"⚠️  {name} zorla kapatılıyor...")
                        process.kill()
                        process.wait()
                        print(f"✅ {name} zorla kapatıldı")
                        
            except Exception as e:
                print(f"❌ {name} durdurulurken hata: {e}")
        
        print("✅ Tüm process'ler durduruldu")
    
    def show_status(self):
        """Process durumlarını göster"""
        print("\n" + "="*60)
        print("📊 DURUM RAPORU")
        print("="*60)
        
        for name, process in self.processes:
            if process.poll() is None:
                status = "🟢 Çalışıyor"
            else:
                status = f"🔴 Durdu (kod: {process.returncode})"
            
            print(f"{name:15} | PID: {process.pid:6} | {status}")
        
        print("="*60)
        print("📊 Dashboard: http://localhost:5001")
        print("❓ Sorular: http://localhost:5001/questions")
        print("🎯 Easy Apply Olmayan: http://localhost:5001/non-easy-apply")
        print("🧪 Test: http://localhost:5001/test-i18n")
        print("="*60)
    
    def interactive_menu(self):
        """Etkileşimli menü"""
        if not self.interactive:
            # Non-interactive mode - just wait for signals
            try:
                while self.running:
                    time.sleep(1)
            except KeyboardInterrupt:
                self.stop_all()
            return

        # Log takibi aktifse, menü gösterme
        if self.with_logs:
            try:
                while self.running:
                    time.sleep(1)
            except KeyboardInterrupt:
                self.stop_all()
            return

        while self.running:
            try:
                print("\n" + "-"*40)
                print("KOMUTLAR:")
                print("s - Durum göster")
                print("r - Process'leri yeniden başlat")
                print("q - Çıkış")
                print("-"*40)

                choice = input("Komut seçin (s/r/q): ").strip().lower()

                if choice == 's':
                    self.show_status()
                elif choice == 'r':
                    print("🔄 Process'ler yeniden başlatılıyor...")
                    self.stop_all()
                    time.sleep(2)
                    self.start()
                elif choice == 'q':
                    self.stop_all()
                    break
                else:
                    print("❌ Geçersiz komut!")

            except KeyboardInterrupt:
                self.stop_all()
                break
            except EOFError:
                self.stop_all()
                break
    
    def start(self, dashboard_only=False):
        """Ana başlatma fonksiyonu"""
        self.print_banner()
        
        # Gerekli dosyaları kontrol et
        if not self.check_requirements():
            print("❌ Gerekli dosyalar eksik. Lütfen kurulumu kontrol edin.")
            return False
        
        # Signal handler'ı ayarla
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        print("🚀 Uygulamalar başlatılıyor...\n")
        
        # Dashboard'u başlat
        dashboard_process = self.start_dashboard()
        if not dashboard_process:
            return False
        
        # Biraz bekle
        time.sleep(3)

        # Bot'u başlat (eğer dashboard_only değilse)
        if not dashboard_only:
            bot_process = self.start_bot()
            if not bot_process:
                print("⚠️  Failed to start Bot, only Dashboard is running")
        else:
            print("ℹ️  Dashboard-only mode, Bot not started")
        
        # Process monitoring başlat
        monitor_threads = self.monitor_processes()

        # Log takip başlat (eğer isteniyorsa)
        log_thread = self.follow_log_file()

        print("\n" + "="*60)
        print("🎉 TÜM SİSTEMLER HAZIR!")
        print("="*60)
        print("📊 Dashboard: http://localhost:5001")
        print("🤖 Bot: Otomatik iş başvuruları çalışıyor")
        if self.with_logs:
            print("📋 Log takibi aktif - Bot logları aşağıda görünecek")
        print("⌨️  Ctrl+C ile çıkış yapabilirsiniz")
        print("="*60)

        # İlk durum raporunu göster (sadece log takibi yoksa)
        if not self.with_logs:
            time.sleep(2)
            self.show_status()

        # Etkileşimli menüyü başlat
        self.interactive_menu()
        
        return True

def main():
    """Ana fonksiyon"""
    parser = argparse.ArgumentParser(description='LinkedIn Auto Job Applier - Unified Starter')
    parser.add_argument('--no-interactive', action='store_true',
                       help='Run in non-interactive mode (no menu)')
    parser.add_argument('--dashboard-only', action='store_true',
                       help='Start only dashboard (no bot)')
    parser.add_argument('--with-logs', action='store_true',
                       help='Show live log output in terminal')
    args = parser.parse_args()

    starter = LinkedInAutoJobApplierStarter(
        interactive=not args.no_interactive,
        with_logs=args.with_logs
    )

    try:
        success = starter.start(dashboard_only=args.dashboard_only)
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n🛑 User cancelled")
        starter.stop_all()
        sys.exit(0)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        starter.stop_all()
        sys.exit(1)

if __name__ == "__main__":
    main()
