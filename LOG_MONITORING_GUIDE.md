# 📋 Log Monitoring Guide

Bu rehber, LinkedIn Auto Job Applier'da log takip özelliklerinin nasıl kullanılacağını açıklar.

## 🚀 Yeni Özellikler

### 1. Timeout Mekanizması
- **Özellik**: <PERSON><PERSON> başvuru sürecinde belirli bir süre geçtikten sonra otomatik timeout
- **Süre**: Varsayılan 120 saniye (2 dakika) - `config/settings.py`'de değiştirilebilir
- **Davranış**: Timeout durumunda başvuru iptal edilir, başar<PERSON>s<PERSON>z olarak kaydedilir ve diğer işe geçilir

### 2. Terminal Log Takibi
- **Özellik**: Bot loglarını terminalde canlı olarak takip etme
- **Avantaj**: Uygulamanın ne yaptığını gerçek zamanlı görebilme

## 📝 Kullanım Komutları

### Make Komutları

```bash
# Normal başlatma (önceki gibi)
make start

# Canlı log takibi ile başlatma
make start-with-logs

# Sadece logları göster (son 20 satır)
make logs

# Logları canlı takip et
make logs-follow

# Durum kontrolü
make status

# Durdur
make stop

# Yardım
make help
```

### Manuel Python Komutları

```bash
# Normal başlatma
python start.py --no-interactive

# Canlı log takibi ile başlatma
python start.py --no-interactive --with-logs

# Sadece dashboard
python start.py --no-interactive --dashboard-only

# Sadece bot
python runAiBot.py
```

## 🔧 Konfigürasyon

### Timeout Süresi Ayarlama

`config/settings.py` dosyasında:

```python
# İş başvuru timeout süresi (saniye)
application_timeout = 120  # 2 dakika

# Daha kısa süre için:
application_timeout = 60   # 1 dakika

# Daha uzun süre için:
application_timeout = 300  # 5 dakika
```

## 📊 Log Çıktısı Örnekleri

### Normal Log Mesajları
```
🤖 Login successful!
🤖 Now searching for "Software Engineer"
🤖 Trying to Apply to "Senior Developer | Tech Company" job. Job ID: 1234567890
🤖 Successfully saved "Senior Developer | Tech Company" job. Job ID: 1234567890 info
```

### Timeout Log Mesajları
```
🤖 Application process timed out after 120 seconds for job: Senior Developer | Tech Company
🤖 Application process timed out after 120 seconds
🤖 Moving to next job...
```

## 🎯 Kullanım Senaryoları

### 1. Normal Kullanım (Arka Planda)
```bash
make start
```
- Dashboard ve bot başlar
- Loglar dosyaya yazılır
- Terminal menüsü kullanılabilir

### 2. Debug/Monitoring (Canlı Takip)
```bash
make start-with-logs
```
- Dashboard ve bot başlar
- Loglar hem dosyaya hem terminale yazılır
- Bot'un ne yaptığını gerçek zamanlı görebilirsiniz
- Ctrl+C ile durdurabilirsiniz

### 3. Sadece Log Takibi
```bash
make logs-follow
```
- Sadece mevcut log dosyasını takip eder
- Bot çalışmıyorsa bile eski logları gösterir

## ⚠️ Önemli Notlar

1. **Timeout Süresi**: Çok kısa ayarlarsanız normal başvurular da timeout olabilir
2. **Log Dosyası**: `logs/log.txt` dosyasında tüm loglar saklanır
3. **Performans**: Log takibi minimal performans etkisi yapar
4. **Kesintisiz Çalışma**: Timeout sayesinde bot takılmaz, sürekli çalışır

## 🐛 Sorun Giderme

### Log Dosyası Bulunamıyor
```bash
# Logs klasörünü kontrol edin
ls -la logs/

# Eğer yoksa oluşturun
mkdir -p logs
```

### Timeout Çalışmıyor
- `config/settings.py`'de `application_timeout` değerini kontrol edin
- Değer pozitif bir sayı olmalı (örn: 120)

### Loglar Görünmüyor
- Bot'un çalıştığından emin olun
- `logs/log.txt` dosyasının var olduğunu kontrol edin

## 📈 Gelişmiş Kullanım

### Birden Fazla Terminal
```bash
# Terminal 1: Bot'u loglarla başlat
make start-with-logs

# Terminal 2: Sadece logları takip et
make logs-follow
```

### Log Analizi
```bash
# Son 50 satırı göster
tail -50 logs/log.txt

# Timeout mesajlarını filtrele
grep -i "timeout" logs/log.txt

# Başarılı başvuruları say
grep -c "Successfully saved" logs/log.txt
```

Bu özellikler sayesinde LinkedIn Auto Job Applier'ı daha etkili şekilde kullanabilir ve sorunları hızlıca tespit edebilirsiniz! 🎉
