# LinkedIn Auto Job Applier - Makefile
# Te<PERSON> komutla hem bot'u hem dashboard'u başlatır
.PHONY: start stop status help install test clean dashboard bot logs logs-follow start-with-logs
# V<PERSON><PERSON><PERSON>lan hedef
all: start
# Main startup command
start:
	@echo "🚀 Starting LinkedIn Auto Job Applier..."
	@echo "📊 Dashboard: http://localhost:5001"
	@echo "🤖 Bot: Automated job applications"
	@echo "⌨️  Press Ctrl+C to stop"
	@echo ""
	@if [ -f "venv/bin/activate" ]; then \
		echo "✅ Activating virtual environment..."; \
		. venv/bin/activate && python start.py --no-interactive; \
	else \
		echo "⚠️  Virtual environment not found, using system Python"; \
		python3 start.py --no-interactive; \
	fi
# Start dashboard only
dashboard:
	@echo "📊 Starting Dashboard only..."
	@if [ -f "venv/bin/activate" ]; then \
		. venv/bin/activate && python start.py --no-interactive --dashboard-only; \
	else \
		python3 start.py --no-interactive --dashboard-only; \
	fi
# Start bot only
bot:
	@echo "🤖 Starting Bot only..."
	@if [ -f "venv/bin/activate" ]; then \
		. venv/bin/activate && python runAiBot.py; \
	else \
		python3 runAiBot.py; \
	fi
# Stop processes
stop:
	@echo "🛑 Stopping processes..."
	@pkill -f "python.*app.py" || true
	@pkill -f "python.*runAiBot.py" || true
	@echo "✅ All processes stopped"
# Check status
status:
	@echo "📊 Process status:"
	@echo "=================="
	@if pgrep -f "python.*app.py" > /dev/null; then \
		echo "Dashboard: 🟢 Running (PID: $$(pgrep -f 'python.*app.py'))"; \
	else \
		echo "Dashboard: 🔴 Stopped"; \
	fi
	@if pgrep -f "python.*runAiBot.py" > /dev/null; then \
		echo "Bot:       🟢 Running (PID: $$(pgrep -f 'python.*runAiBot.py'))"; \
	else \
		echo "Bot:       🔴 Stopped"; \
	fi
	@echo "=================="
	@echo "📊 Dashboard: http://localhost:5001"
# Install required packages
install:
	@echo "📦 Installing required packages..."
	@if [ ! -d "venv" ]; then \
		echo "🔧 Creating virtual environment..."; \
		python3 -m venv venv; \
	fi
	@echo "✅ Activating virtual environment..."
	@. venv/bin/activate && pip install --upgrade pip
	@. venv/bin/activate && pip install -r requirements.txt
	@echo "✅ Installation completed!"
# Run tests
test:
	@echo "🧪 Running tests..."
	@if [ -f "venv/bin/activate" ]; then \
		. venv/bin/activate && python -m pytest tests/ -v; \
	else \
		python3 -m pytest tests/ -v; \
	fi
# Open test page
test-i18n:
	@echo "🧪 Opening language test page..."
	@open http://localhost:5001/test-i18n || xdg-open http://localhost:5001/test-i18n || echo "Open http://localhost:5001/test-i18n in your browser"
# Clean up
clean:
	@echo "🧹 Cleaning up..."
	@find . -type f -name "*.pyc" -delete
	@find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	@rm -f *.log
	@echo "✅ Cleanup completed"
# Show log files
logs:
	@echo "📋 Log files:"
	@echo "================"
	@if [ -f "logs/log.txt" ]; then \
		echo "🤖 Bot Log:"; \
		tail -20 logs/log.txt; \
		echo ""; \
	fi
	@if [ -f "dashboard.log" ]; then \
		echo "📊 Dashboard Log:"; \
		tail -20 dashboard.log; \
		echo ""; \
	fi
# Follow log files in real-time
logs-follow:
	@echo "📋 Following log files in real-time..."
	@echo "⌨️  Press Ctrl+C to stop"
	@echo "================"
	@if [ -f "logs/log.txt" ]; then \
		tail -f logs/log.txt; \
	else \
		echo "❌ Bot log file not found: logs/log.txt"; \
	fi
# Start with live logs
start-with-logs:
	@echo "🚀 Starting LinkedIn Auto Job Applier with live logs..."
	@echo "📊 Dashboard: http://localhost:5001"
	@echo "🤖 Bot: Automated job applications"
	@echo "⌨️  Press Ctrl+C to stop"
	@echo ""
	@if [ -f "venv/bin/activate" ]; then \
		echo "✅ Activating virtual environment..."; \
		. venv/bin/activate && python start.py --no-interactive --with-logs; \
	else \
		echo "⚠️  Virtual environment not found, using system Python"; \
		python3 start.py --no-interactive --with-logs; \
	fi
# Developer mode (debug)
dev:
	@echo "🔧 Starting developer mode..."
	@echo "📊 Dashboard: http://localhost:5001"
	@echo "🧪 Test: http://localhost:5001/test-i18n"
	@if [ -f "venv/bin/activate" ]; then \
		. venv/bin/activate && FLASK_ENV=development FLASK_DEBUG=1 python app.py; \
	else \
		FLASK_ENV=development FLASK_DEBUG=1 python3 app.py; \
	fi
# Help
help:
	@echo "LinkedIn Auto Job Applier - Makefile Commands"
	@echo "=============================================="
	@echo ""
	@echo "Main Commands:"
	@echo "  make start           - Start both bot and dashboard"
	@echo "  make start-with-logs - Start with live log output"
	@echo "  make stop            - Stop all processes"
	@echo "  make status          - Show process status"
	@echo ""
	@echo "Individual Commands:"
	@echo "  make dashboard - Start dashboard only"
	@echo "  make bot       - Start bot only"
	@echo "  make dev       - Developer mode (debug)"
	@echo ""
	@echo "Setup and Test:"
	@echo "  make install   - Install required packages"
	@echo "  make test      - Run tests"
	@echo "  make test-i18n - Open language test page"
	@echo ""
	@echo "Utility Commands:"
	@echo "  make logs        - Show recent log entries"
	@echo "  make logs-follow - Follow logs in real-time"
	@echo "  make clean       - Clean up"
	@echo "  make help        - Show this help message"
	@echo ""
	@echo "Usage Examples:"
	@echo "  make                 - Default (start command)"
	@echo "  make start-with-logs - Start with live logs"
	@echo "  make logs-follow     - Watch logs in real-time"
	@echo "  make status          - Check status"
	@echo "  make stop            - Stop"
