@echo off
chcp 65001 >nul
title LinkedIn Auto Job Applier - Unified Starter

:: Renk<PERSON> çıktı için
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "PURPLE=[95m"
set "CYAN=[96m"
set "NC=[0m"

:: Banner göster
echo %BLUE%
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                LinkedIn Auto Job Applier                     ║
echo ║                    Unified Starter                           ║
echo ╠══════════════════════════════════════════════════════════════╣
echo ║  🤖 Bot: Otomatik iş başvuruları                            ║
echo ║  📊 Dashboard: Web arayüzü (http://localhost:5001)          ║
echo ║  ❓ Sorular: Soru yönetimi                                  ║
echo ║  🎯 Easy Apply Olmayan: Manuel başvuru yönetimi             ║
echo ╚══════════════════════════════════════════════════════════════╝
echo %NC%

:: Gerekli dosyaları kontrol et
echo %CYAN%🔍 Gerekli dosyalar kontrol ediliyor...%NC%

if not exist "app.py" (
    echo %RED%❌ app.py bulunamadı%NC%
    pause
    exit /b 1
)

if not exist "runAiBot.py" (
    echo %RED%❌ runAiBot.py bulunamadı%NC%
    pause
    exit /b 1
)

if not exist "venv" (
    echo %RED%❌ Virtual environment (venv) bulunamadı%NC%
    pause
    exit /b 1
)

echo %GREEN%✅ Tüm gerekli dosyalar mevcut%NC%

:: Virtual environment'ı aktive et
if exist "venv\Scripts\activate.bat" (
    call venv\Scripts\activate.bat
    echo %GREEN%✅ Virtual environment aktive edildi%NC%
) else (
    echo %YELLOW%⚠️  Virtual environment bulunamadı, system Python kullanılıyor%NC%
)

echo.
echo %CYAN%🚀 Uygulamalar başlatılıyor...%NC%
echo.

:: Dashboard'u başlat
echo %CYAN%🚀 Dashboard başlatılıyor...%NC%
start "LinkedIn Dashboard" /min python app.py
timeout /t 3 /nobreak >nul
echo %GREEN%✅ Dashboard başlatıldı%NC%
echo %BLUE%📊 Web arayüzü: http://localhost:5001%NC%

:: Bot'u başlat
echo %CYAN%🤖 Bot başlatılıyor...%NC%
start "LinkedIn Bot" /min python runAiBot.py
timeout /t 3 /nobreak >nul
echo %GREEN%✅ Bot başlatıldı%NC%

echo.
echo %GREEN%🎉 TÜM SİSTEMLER HAZIR!%NC%
echo ============================================
echo %BLUE%📊 Dashboard: http://localhost:5001%NC%
echo %BLUE%❓ Sorular: http://localhost:5001/questions%NC%
echo %BLUE%🎯 Easy Apply Olmayan: http://localhost:5001/non-easy-apply%NC%
echo %BLUE%🧪 Test: http://localhost:5001/test-i18n%NC%
echo ============================================
echo.
echo %YELLOW%⌨️  Dashboard ve Bot ayrı pencereler halinde çalışıyor%NC%
echo %YELLOW%⌨️  Bu pencereyi kapatabilirsiniz%NC%
echo %YELLOW%⌨️  Uygulamaları durdurmak için Task Manager kullanın%NC%
echo.

:: Tarayıcıyı aç
echo %CYAN%🌐 Tarayıcı açılıyor...%NC%
timeout /t 2 /nobreak >nul
start http://localhost:5001

echo.
echo %GREEN%✅ Kurulum tamamlandı!%NC%
echo.
pause
