# 🚀 LinkedIn Auto Job Applier - Quick Start

Start both bot and dashboard with a single command!

## 📋 Gereksinimler

- Python 3.7+
- Virtual environment (venv)
- Gerekli Python paketleri (requirements.txt)

## ⚡ Tek Komut Başlatma

### 🎯 En Kolay Yöntem - Makefile (Önerilen)

```bash
# Her şeyi başlat (bot + dashboard)
make

# veya
make start
```

**Diğer Makefile komutları:**

```bash
make help        # Tüm komutları göster
make status      # Durum kontrol et
make stop        # Durdur
make dashboard   # Sadece dashboard
make bot         # Sadece bot
make install     # Pak<PERSON>leri yükle
make clean       # Te<PERSON>zlik yap
```

### 🐍 Python Script

```bash
# Etkileşimli başlatma
python3 start.py

# veya
./start.py
```

### 🖥️ Shell Script (Linux/Mac)

```bash
# Bash script ile başlat
./start.sh
```

### 🪟 Windows Batch

```cmd
# Windows için
start.bat
```

## 📊 Web Arayüzleri

Başlatma sonrası şu adreslere erişebilirsiniz:

- **📊 Ana Dashboard**: http://localhost:5001/
- **❓ Soru Yönetimi**: http://localhost:5001/questions
- **🎯 Easy Apply Olmayan İşler**: http://localhost:5001/non-easy-apply
- **🧪 Dil Testi**: http://localhost:5001/test-i18n

## 🔧 Başlatma Seçenekleri

### 1. 🎯 Tam Sistem (Önerilen)

```bash
make start
```

- ✅ Dashboard çalışır
- ✅ Bot çalışır
- ✅ Tüm özellikler aktif

### 2. 📊 Sadece Dashboard

```bash
make dashboard
```

- ✅ Web arayüzü
- ❌ Otomatik başvuru yok

### 3. 🤖 Sadece Bot

```bash
make bot
```

- ✅ Otomatik başvuru
- ❌ Web arayüzü yok

### 4. 🔧 Geliştirici Modu

```bash
make dev
```

- ✅ Debug modu
- ✅ Otomatik yenileme
- ✅ Detaylı loglar

## 🛑 Durdurma

### Makefile ile:

```bash
make stop
```

### Manuel:

- **Ctrl+C** (terminal'de)
- **Task Manager** (Windows)
- **Activity Monitor** (Mac)

## 📊 Durum Kontrolü

```bash
# Process durumunu kontrol et
make status

# Log dosyalarını göster
make logs
```

## 🔧 Sorun Giderme

### 1. Port Sorunu (5001 kullanımda)

```bash
# Çalışan process'i bul ve durdur
make stop

# veya manuel
lsof -ti:5001 | xargs kill -9
```

### 2. Virtual Environment Sorunu

```bash
# Paketleri yeniden yükle
make install
```

### 3. Python Bulunamadı

```bash
# Python versiyonunu kontrol et
python3 --version

# Virtual environment'ı kontrol et
ls -la venv/
```

### 4. Gerekli Dosyalar Eksik

```bash
# Dosyaları kontrol et
ls -la app.py runAiBot.py requirements.txt
```

## 📋 Başlatma Sırası

1. **Gereksinimler kontrol edilir**
2. **Virtual environment aktive edilir**
3. **Dashboard başlatılır** (http://localhost:5001)
4. **Bot başlatılır** (arka planda)
5. **Durum raporu gösterilir**
6. **Etkileşimli menü açılır**

## 🎮 Etkileşimli Komutlar

Başlatma sonrası kullanabileceğiniz komutlar:

- **s** - Durum göster
- **l** - Log dosyalarını takip et
- **r** - Process'leri yeniden başlat
- **q** - Çıkış

## 📱 Mobil Erişim

Dashboard mobil uyumludur:

```
http://[bilgisayar-ip]:5001
```

## 🌍 Dil Desteği

- **🇹🇷 Türkçe** (varsayılan)
- **🇺🇸 İngilizce**

Web arayüzünde sağ üstteki dil menüsünden değiştirebilirsiniz.

## 📊 Özellikler

### 🤖 Bot Özellikleri:

- Otomatik iş arama
- Akıllı başvuru sistemi
- Soru-cevap yönetimi
- Easy Apply olmayan iş tespiti
- Hata kurtarma sistemi

### 📊 Dashboard Özellikleri:

- Real-time istatistikler
- İş filtreleme ve arama
- Grafik görünümler
- Soru yönetimi
- Çoklu dil desteği

## 🆘 Acil Durum

Eğer sistem yanıt vermiyorsa:

```bash
# Tüm Python process'lerini durdur
pkill -f python

# Port'u temizle
lsof -ti:5001 | xargs kill -9

# Yeniden başlat
make start
```

## 📞 Destek

Sorun yaşıyorsanız:

1. **Durum kontrol edin**: `make status`
2. **Log'ları kontrol edin**: `make logs`
3. **Temizlik yapın**: `make clean`
4. **Yeniden başlatın**: `make start`

## 🎯 Hızlı Komut Referansı

```bash
# Başlat
make

# Durdur
make stop

# Durum
make status

# Yardım
make help

# Temizlik
make clean

# Kurulum
make install
```

---

**🎉 Artık tek komutla her şey hazır!**

`make` yazın ve LinkedIn Auto Job Applier'ın tüm gücünden yararlanın! 🚀
